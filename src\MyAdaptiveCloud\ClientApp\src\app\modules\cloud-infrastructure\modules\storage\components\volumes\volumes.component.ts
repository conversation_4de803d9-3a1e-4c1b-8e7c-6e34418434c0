import { DecimalPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal, TemplateRef, viewChild } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { Volume } from '@app/modules/cloud-infrastructure/models/volumes/volume';
import { VolumeType } from '@app/modules/cloud-infrastructure/models/volumes/volume-type';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { ApiDatasetResult } from '@app/shared/models/api-service/api.dataset.result';
import { BaseListComponent } from '@app/shared/models/datatable/base-list-component.model';
import { FileSizeConverterPipe } from '@app/shared/pipes/file-size-converter.pipe';
import { UserContextService } from '@app/shared/services/user-context.service';
import { sortByProperty } from '@app/shared/utils/helpers';
import { NgbDropdown, NgbDropdownItem, NgbDropdownMenu, NgbDropdownToggle } from '@ng-bootstrap/ng-bootstrap';
import { DatatableComponent, TableColumn } from '@swimlane/ngx-datatable';
import { filter, Observable, of, skip, take } from 'rxjs';
import { VolumeState } from '../../../../models/volumes/volume-state';
import { StorageVolumeViewModel } from '../../models/storage-volume.view-model';
import { VolumesMetricsRequest } from '../../requests/volumes-metrics.request';
import { VolumesPermissionService } from '../../services/volumes-permission.service';
import { VolumesService } from '../../services/volumes.service';

@Component({
    selector: 'app-volumes',
    imports: [AutoSearchBoxComponent, DatatableComponent, NgbDropdown, NgbDropdownToggle, NgbDropdownMenu, NgbDropdownItem, DecimalPipe, FileSizeConverterPipe],
    templateUrl: './volumes.component.html',
    styleUrl: './volumes.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class VolumesComponent extends BaseListComponent<StorageVolumeViewModel> implements OnInit {
    private readonly store = inject(ZoneDomainAccountStore);
    private readonly userContextService = inject(UserContextService);
    protected readonly volumesPermissionService = inject(VolumesPermissionService);
    protected readonly cloudInfraPermissionService = inject(CloudInfraPermissionService);
    private readonly volumesService = inject(VolumesService);

    protected readonly viewMode = signal<'volumes' | 'metrics'>('volumes');
    private readonly volumes = signal<StorageVolumeViewModel[]>(null);

    private readonly headerTemplate = viewChild<TemplateRef<never>>('headerTemplate');
    private readonly stateCell = viewChild<TemplateRef<never>>('stateCell');
    private readonly numberCell = viewChild<TemplateRef<never>>('numberCell');
    private readonly gbCell = viewChild<TemplateRef<never>>('gbCell');

    protected readonly volumeType = VolumeType;
    protected readonly volumeState = VolumeState;

    private readonly columnsName: { [K in keyof StorageVolumeViewModel]?: K } & { actions: string } = {
        state: 'state',
        name: 'name',
        vmName: 'vmName',
        zoneName: 'zoneName',
        domain: 'domain',
        account: 'account',
        actions: 'actions',
        type: 'type',
        size: 'size',
        physicalSize: 'physicalSize',
        utilization: 'utilization',
        diskRead: 'diskRead',
        diskWrite: 'diskWrite',
        diskIOPS: 'diskIOPS'
    };

    private metricColumns: TableColumn<StorageVolumeViewModel>[];
    private volumeColumns: TableColumn<StorageVolumeViewModel>[];

    private readonly selectedDomain$ = toObservable(this.store.selectedDomain);
    private readonly selectedAccount$ = toObservable(this.store.selectedAccount);

    private get ciUserContext() {
        return this.userContextService.currentUser.cloudInfraUserContext;
    }

    constructor() {
        super();
        this.pagination = new VolumesMetricsRequest();
    }

    ngOnInit(): void {
        const sharedColumns: TableColumn[] = [
            {
                name: 'State',
                prop: this.columnsName.state,
                cellTemplate: this.stateCell(),
                headerTemplate: this.headerTemplate(),
                sortable: true,
                canAutoResize: false,
                width: 100
            },
            {
                name: 'Name',
                prop: this.columnsName.name,
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 200
            },
            {
                name: 'Virtual Machine',
                prop: this.columnsName.vmName,
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 200,
            }
        ];

        const actionColumn = {
            name: 'Actions',
            width: 100,
            cellTemplate: this.actionsTemplate(),
            sortable: false,
            canAutoResize: false
        };

        const domainColumn = { //  only if domains under scoped
            sortable: false,
            resizeable: false,
            canAutoResize: false,
            width: 120,
            prop: this.columnsName.domain,
            name: 'Domain'
        };

        const accountColumn = { //  only if scoped to a domain
            headerTemplate: this.headerTemplateSortable(),
            sortable: true,
            resizeable: false,
            canAutoResize: false,
            width: 120,
            prop: this.columnsName.account,
            name: 'Account',
        };

        this.volumeColumns = sharedColumns.concat([
            domainColumn,
            accountColumn,
            {
                name: 'Size',
                prop: this.columnsName.size,
                headerTemplate: this.headerTemplateSortable(),
                cellTemplate: this.gbCell(),
                sortable: true,
                canAutoResize: false,
                resizeable: false,
                width: 100
            },
            {
                name: 'Physical Size',
                prop: this.columnsName.physicalSize,
                headerTemplate: this.headerTemplateSortable(),
                cellTemplate: this.gbCell(),
                sortable: true,
                canAutoResize: false,
                resizeable: false,
                width: 100
            },
            {
                name: 'Utilization',
                prop: this.columnsName.utilization,
                headerTemplate: this.headerTemplateSortable(),
                sortable: true,
                resizeable: false,
                canAutoResize: false,
                width: 100
            },
            actionColumn
        ]);

        this.metricColumns = sharedColumns.concat([
            {
                sortable: false,
                resizeable: false,
                canAutoResize: false,
                width: 120,
                prop: this.columnsName.zoneName,
                name: 'Zone'
            },
            domainColumn,
            accountColumn,
            {
                name: 'Disk Read (KiB)',
                prop: this.columnsName.diskRead,
                headerTemplate: this.headerTemplateSortable(),
                cellTemplate: this.numberCell(),
                sortable: true,
                canAutoResize: false,
                width: 150
            },
            {
                name: 'Disk Write (KiB)',
                prop: this.columnsName.diskWrite,
                headerTemplate: this.headerTemplateSortable(),
                cellTemplate: this.numberCell(),
                sortable: true,
                canAutoResize: false,
                width: 150
            },
            {
                name: 'Disk IOPS',
                prop: this.columnsName.diskIOPS,
                headerTemplate: this.headerTemplateSortable(),
                cellTemplate: this.numberCell(),
                sortable: true,
                canAutoResize: false,
                width: 150
            },
            actionColumn
        ]);

        // When the user context has its own domain, do not pass account so the result includes all volumes in the domain
        const accountName = this.ciUserContext.hasMappedDomain ? null : this.ciUserContext.accountName;
        this.volumesService.getVolumes(this.ciUserContext.domainId, accountName)
            .pipe(take(1))
            .subscribe(res => {
                this.volumes.set(this.mapVolumesResponse(res));
                this.onDomainAccountChanged(this.ciUserContext.domainId, accountName);
            });

        this.selectedDomain$.pipe(
            skip(1),
            filter(domain => !!domain),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(domain => {
            this.onDomainAccountChanged(domain.id, this.store.getAccount());
        });

        this.selectedAccount$.pipe(
            skip(1),
            filter(account => !!account),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe(account => {
            this.onDomainAccountChanged(this.store.getDomainId(), account.name);
        });
    }

    private mapVolumesResponse(volumes: Volume[]): StorageVolumeViewModel[] {
        let volumesVM: StorageVolumeViewModel[] = [];
        if (volumes?.length > 0) {
            volumesVM = volumes.map(v => {
                const volumeVM: StorageVolumeViewModel = {
                    id: v.id,
                    name: v.name,
                    account: v.account?.trim(),
                    domain: v.domain?.trim(),
                    zoneName: v.zonename,
                    state: v.state,
                    vmName: v.vmdisplayname?.trim() ?? v.vmname?.trim() ?? '',
                    isAttached: !!v.virtualmachineid,
                    domainId: v.domainid,
                    diskIOPS: (v.diskioread || 0) + (v.diskiowrite || 0),
                    diskRead: v.diskkbsread,
                    diskWrite: v.diskiowrite,
                    physicalSize: v.physicalsize ?? 0,
                    size: v.size ?? 0,
                    utilization: v.utilization ?? '-',
                    type: v.type
                };
                return volumeVM;
            });
        }
        return volumesVM;
    }

    private onDomainAccountChanged(domainId: string, account: string): void {
        let columns = [...this.viewMode() === 'metrics' ? this.metricColumns : this.volumeColumns];
        if (account) {
            columns = columns.filter(c => c.prop !== this.columnsName.account && c.prop !== this.columnsName.domain);
        } else if (domainId && !this.store.isRootDomainSelected()) {
            columns = columns.filter(c => c.prop !== this.columnsName.domain);
        }

        const table = this.table();
        if (table.columns?.length) {
            table.columns = columns;
        }

        super.initialize(() => this.getFilteredAndPaginatedVolumes$(), columns);
        table.sorts = [{ prop: this.columnsName.name, dir: 'asc' }];
    }

    private getFilteredAndPaginatedVolumes$(): Observable<ApiDatasetResult<StorageVolumeViewModel[]>> {
        // Apply filters, including selected domain and account, search term and zone and state filters
        const selectedDomain = this.store.selectedDomain();
        const selectedAccount = this.store.selectedAccount();
        const searchTerm = this.pagination?.searchTerm?.toLowerCase();

        const filteredVolumes = [...this.volumes().filter(v => {
            const matchesDomain = !selectedDomain || v.domainId === selectedDomain.id;
            const matchesAccount = !selectedAccount || v.account === selectedAccount.name;
            const matchesSearchTerm = !searchTerm || v.name.toLowerCase().includes(searchTerm);
            return matchesDomain && matchesAccount && matchesSearchTerm;
        })];

        // Apply sorting
        const ascOrder = this.pagination.orderDir === 'asc';
        if (this.pagination.orderBy === this.columnsName.state) {
            filteredVolumes.sort(sortByProperty(this.columnsName.state, ascOrder));
        } else if (this.pagination.orderBy === this.columnsName.name) {
            filteredVolumes.sort(sortByProperty(this.columnsName.name, ascOrder));
        } else if (this.pagination.orderBy === this.columnsName.account) {
            filteredVolumes.sort(sortByProperty(this.columnsName.account, ascOrder));
        } else if (this.pagination.orderBy === this.columnsName.physicalSize) {
            filteredVolumes.sort(sortByProperty(this.columnsName.physicalSize, ascOrder));
        } else if (this.pagination.orderBy === this.columnsName.size) {
            filteredVolumes.sort(sortByProperty(this.columnsName.size, ascOrder));
        } else if (this.pagination.orderBy === this.columnsName.diskRead) {
            filteredVolumes.sort(sortByProperty(this.columnsName.diskRead, ascOrder));
        } else if (this.pagination.orderBy === this.columnsName.diskWrite) {
            filteredVolumes.sort(sortByProperty(this.columnsName.diskWrite, ascOrder));
        } else if (this.pagination.orderBy === this.columnsName.diskIOPS) {
            filteredVolumes.sort(sortByProperty(this.columnsName.diskIOPS, ascOrder));
        } else if (this.pagination.orderBy === this.columnsName.utilization) {
            filteredVolumes.sort(sortByProperty(this.columnsName.utilization, ascOrder));
        }

        // Apply pagination
        const startIndex = (this.pagination.currentPage - 1) * this.pagination.pageSize;
        const endIndex = startIndex + this.pagination.pageSize;
        const paginatedList = filteredVolumes.slice(startIndex, endIndex);
        return of({ data: paginatedList, totalCount: filteredVolumes?.length ?? 0 });
    }

    protected toggleViewMode(mode: 'volumes' | 'metrics'): void {
        this.viewMode.set(mode);
        let columns = mode === 'metrics' ? this.metricColumns : this.volumeColumns;
        if (this.store.selectedAccount()) {
            columns = columns.filter(c => c.prop !== this.columnsName.account && c.prop !== this.columnsName.domain);
        } else if (this.store.selectedDomain() && !this.store.isRootDomainSelected()) {
            columns = columns.filter(c => c.prop !== this.columnsName.domain);
        }
        this.table().columns = [...columns];
        this.table().recalculate();
    }
}
