import { inject, Injectable } from '@angular/core';
import { Volume } from '@app/modules/cloud-infrastructure/models/volumes/volume';
import { VOLUMES_ENDPOINT_NAMES } from '@app/modules/cloud-infrastructure/models/volumes/volume.constants';
import { ListVolume, ListVolumeResponse } from '@app/modules/cloud-infrastructure/responses/list-volume.response';
import { CloudInfraParamsEnum } from '@app/shared/models/cloud-infra/params.enum';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { forkJoin, map, Observable, of, switchMap } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class VolumesService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    /*
    * This method retrieves a list of volumes for a given domain and account.
    * The domainId argument is required and it expects either the domainId in the cloud infra user context.
    * The account argument is optional and it should only be used when the context does not have its own domain.
    */
    getVolumes(domainId: string, account: string | null): Observable<Volume[]> {
        const pageSize = 500;

        // Fetch the first batch, which will return the total count, then fetch the rest of the records in parallel
        return this.getVolumesBatch(domainId, account, 1, pageSize).pipe(
            map(res => {
                const records = [...res?.volume ?? []];
                const remainingRecords = res.count - pageSize;

                if (remainingRecords > 0) {
                    const countOfRequestBatches = Math.ceil(remainingRecords / pageSize);
                    const requests: Observable<ListVolume>[] = [];
                    for (let i = 2; i <= countOfRequestBatches + 1; i++) {
                        requests.push(this.getVolumesBatch(domainId, account, i, pageSize));
                    }
                    return forkJoin(requests).pipe(map(responses => {
                        responses.forEach(response => {
                            records.push(...response.volume);
                        });
                        return records;
                    }));
                }
                return of(records);

            }),
            switchMap(records => records)
        );
    }

    private getVolumesBatch(domainId: string, account: string | null, currentPage: number, pageSize: number): Observable<ListVolume> {
        const params: Record<string, string> = {
            command: VOLUMES_ENDPOINT_NAMES.listVolumes,
            pagesize: pageSize.toString(),
            page: currentPage.toString(),
            isrecursive: 'true',
            listall: 'true'
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;

        if (account) {
            params[CloudInfraParamsEnum.ACCOUNT] = account;
        }

        return this.cloudInfraApiService.get<ListVolumeResponse>(params)
            .pipe(map((response: ListVolumeResponse) => response.listvolumesresponse));
    }
}
