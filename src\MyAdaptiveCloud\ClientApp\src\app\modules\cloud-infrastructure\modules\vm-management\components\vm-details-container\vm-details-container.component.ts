import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, input, OnDestroy, OnInit } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { VmStateEnum } from '@app/shared/models/cloud-infra/vm-state.enum';
import { CloudInfrastructureJobQueueService } from '@app/shared/services/cloud-infrastructure-job-queue.service';
import { NgbDropdown, NgbDropdownMenu, NgbDropdownToggle, NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { filter, map, switchMap } from 'rxjs';
import { VM_ROUTE_SEGMENTS } from '../../constants/vm-management-route-segments';
import { VmActionsService } from '../../services/vm-actions.service';
import { VmDetailsStateService } from '../../services/vm-details.state.service';
import { VmManagementPermissionService } from '../../services/vm-management-permission.service';
import { VmManagementService } from '../../services/vm-management.service';

@Component({
    selector: 'app-vm-details-container',
    imports: [RouterLink, RouterOutlet, RouterLinkActive, NgbPopover, DatePipe, NgbDropdown,
        NgbDropdownMenu, NgbDropdownToggle],
    templateUrl: './vm-details-container.component.html',
    styleUrl: './vm-details-container.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class VmDetailsContainerComponent implements OnInit, OnDestroy {

    readonly vmId = input.required<string>();

    private readonly cloudInfrastructureJobQueueService = inject(CloudInfrastructureJobQueueService);
    private readonly vmManagementService = inject(VmManagementService);
    private readonly destroyRef = inject(DestroyRef);
    private readonly vmDetailsStateService = inject(VmDetailsStateService);

    protected readonly vmActionsService = inject(VmActionsService);
    protected readonly cloudInfraPermissionService = inject(CloudInfraPermissionService);
    protected readonly vmPermissionService = inject(VmManagementPermissionService);

    protected readonly VM_ROUTE_SEGMENTS = VM_ROUTE_SEGMENTS;
    protected readonly vmStateEnum = VmStateEnum;

    protected readonly vm = this.vmDetailsStateService.selectedVM;
    protected readonly isLoading = toSignal(this.vmDetailsStateService.isLoading$);

    ngOnInit(): void {
        this.cloudInfrastructureJobQueueService.updates$
            .pipe(
                filter(vmsIds => Array.isArray(vmsIds) && vmsIds.length > 0 && !!this.vm()),
                switchMap(() => this.vmManagementService.getVirtualMachineListByIds([this.vm().id])
                    .pipe(map(res => {
                        if (res?.length > 0) {
                            return this.vmDetailsStateService.mapVmInstanceToVmDetails(res[0]);
                        }
                        return null;
                    }))),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(vmDetail => {
                this.vmDetailsStateService.selectedVM.set(vmDetail);
                this.vmDetailsStateService.isLoading$.next(false);
            });

        this.vmActionsService.actionExecuted$
            .pipe(
                filter(action => action.virtualMachineId === this.vmId()),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(() => this.vmDetailsStateService.isLoading$.next(true));
    }

    ngOnDestroy(): void {
        this.vmDetailsStateService.selectedVM.set(null);
    }

}
