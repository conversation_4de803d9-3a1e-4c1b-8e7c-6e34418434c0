using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using MyAdaptiveCloud.Api.Authorization;
using MyAdaptiveCloud.Api.Identity;
using MyAdaptiveCloud.Api.Requests.CloudInfrastructure;
using MyAdaptiveCloud.Api.ViewModel;
using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Core.Permissions;
using MyAdaptiveCloud.Services.DTOs.CloudInfra;
using MyAdaptiveCloud.Services.Services;

namespace MyAdaptiveCloud.Api.Controllers
{
    public class CloudInfrastructureController : AuthenticatedControllerBase
    {
        private readonly ICloudInfraService _cloudInfraService;
        private readonly IAdaptiveCloudService _adaptiveCloudService;

        public CloudInfrastructureController(
            ICloudInfraService cloudInfraService,
            IIdentityService identityService,
            IAdaptiveCloudService adaptiveCloudService,
            IMapper mapper)
            : base(identityService, mapper)
        {
            _cloudInfraService = cloudInfraService;
            _adaptiveCloudService = adaptiveCloudService;
        }

        [OrgAuthorize(Perms.ManageCloudInfra)]
        [HttpGet("accounts/{organizationId:int}")]
        public async Task<ActionResult<ApiDataSetResult<List<AccountListModel>>>> GetAccounts(int organizationId)
        {
            var accounts = await _cloudInfraService.GetCloudInfastructureAccounts(organizationId);

            return new ApiDataSetResult<List<AccountListModel>>
            {
                Data = accounts,
                TotalCount = accounts.Count,
            };
        }

        [OrgAuthorize(Perms.ManageCloudInfra)]
        [HttpPost("account/{organizationId:int}")]
        public async Task<ActionResult<ApiResult>> CreateCloudInfraAccount([FromBody] RegisterCloudInfrastructureAccountRequest request, [FromRoute] int organizationId)
        {
            var message = await _cloudInfraService.CreateCloudInfraAccount(request.UserId.Value, request.OrganizationId.Value);

            return new ApiResult
            {
                Message = message
            };
        }

        [OrgAuthorize(Perms.ManageCloudInfra)]
        [HttpDelete("user/{organizationId}/{userId}")]
#pragma warning disable IDE0060 // Remove unused parameter
        public async Task<ActionResult<ApiResult>> DeleteUser(int organizationId, string userId)
#pragma warning restore IDE0060 // Remove unused parameter        
        {
            await _cloudInfraService.DeleteUser(userId);

            return new ApiResult
            {
                Message = "User access removed successfully.",
            };
        }

        [OrgAuthorize(Perms.ManageCloudInfra)]
        [HttpPost("user/{organizationId:int}")]
        public async Task<ActionResult<ApiResult>> CreateUser(int organizationId, [FromBody] CreateCloudInfrastructureUserRequest request)
        {
            await _cloudInfraService.CreateUser(organizationId, request.UserId.Value);
            return CreatedAtAction(nameof(CreateUser), new ApiResult
            {
                Message = "User added successfully."
            });
        }

        [OrgAuthorize(Perms.ManageCloudInfra)]
        [HttpGet("users/{organizationId:int}")]
        public async Task<ActionResult<ApiDataSetResult<List<CloudInfraUserListModel>>>> GetUsers([FromRoute] int organizationId, [FromQuery] bool showSubOrgUsers)
        {
            var users = await _cloudInfraService.GetUsers(organizationId, showSubOrgUsers);
            return new ApiDataSetResult<List<CloudInfraUserListModel>>
            {
                Data = users,
                TotalCount = users.Count
            };
        }

        [OrgAuthorize(Perms.ManageCloudInfra)]
        [HttpPost("user/{organizationId:int}/move")]
#pragma warning disable IDE0060 // Remove unused parameter
        public async Task<ActionResult<ApiResult>> UpdateUserOrganization(int organizationId, [FromBody] UpdateUserAccountRequest request)
#pragma warning restore IDE0060 // Remove unused parameter 
        {
            await _cloudInfraService.UpdateUserOrganization(request.CloudInfraUserId, request.UserId, request.OrganizationId.Value);

            return new ApiResult
            {
                Message = "User Organization updated successfully."
            };
        }

        [OrgAuthorize(Perms.ManageCloudInfra)]
        [HttpGet("{organizationId:int}/isMapped")]
        public async Task<ActionResult<ApiDataResult<bool>>> GetIsOrganizationMappedToCloudInfra(int organizationId)
        {
            var isMapped = await _cloudInfraService.GetIsOrganizationMappedToCloudInfra(organizationId);
            return new ApiDataResult<bool>
            {
                Data = isMapped
            };
        }


        [OrgAuthorize(Perms.ManageCloudInfra)]
        [HttpGet("users/{organizationId:int}/domain-admin")]
        public async Task<ActionResult<ApiDataResult<DomainAdminPanelUsersResponseDTO>>> GetDomainAdminPanelUsers(int organizationId)
        {
            var users = await _cloudInfraService.GetDomainAdminPanelUsers(organizationId);
            return new ApiDataResult<DomainAdminPanelUsersResponseDTO>
            {
                Data = users
            };
        }


        [OrgAuthorize(Perms.ManageCloudInfra)]
        [HttpPost("user/{organizationId:int}/domain-admin")]
        public async Task<ActionResult<ApiResult>> CreateUser(int organizationId, CreateDomainAdminRequest request)
        {
            await _cloudInfraService.CreateUser(request.DomainAccountOrganizationId, request.UserId);
            return CreatedAtAction(nameof(CreateUser), new ApiResult
            {
                Message = "User added successfully."
            });
        }

        [OrgAuthorize(Perms.ManageCloudInfra)]
        [HttpGet("account/{organizationId:int}/role")]
        public async Task<ActionResult<ApiDataResult<CloudInfrastructureOrganizationDTO>>> GetRoleAndAccount(int organizationId)
        {
            var dto = new CloudInfrastructureOrganizationDTO();
            dto.Role = _cloudInfraService.GetRole(organizationId);
            dto.Account = await _cloudInfraService.GetCloudInfraAccount(organizationId);
            dto.CanBeProvisioned = await _cloudInfraService.CanBeProvisioned(organizationId);
            return new ApiDataResult<CloudInfrastructureOrganizationDTO>
            {
                Data = dto
            };
        }


        [FeatureFlagsAuthorize(FeatureFlagConstants.FeatureFlagVirtualMachines, FeatureFlagConstants.FeatureFlagNetworking,
            FeatureFlagConstants.FeatureFlagCloudInfrastructureStorage)]
        [CloudInfraMappingAuthorize(Perms.ManageCloudInfra)]
        [HttpGet("login/{organizationId:int}")]
        public async Task<ActionResult<ApiDataResult<CloudInfraUserContext>>> GetCloudInfraUserContext(int organizationId)
        {
            var userId = _identityService.PersonIdFromPrincipal(User);
            var context = await _cloudInfraService.GetCloudInfraUserContext(organizationId, userId);

            return new ApiDataResult<CloudInfraUserContext>
            {
                Data = context
            };
        }

        [OrgAuthorize(Perms.ViewCloudInfraUsage, Perms.ManageCloudInfraLimits)]
        [HttpGet("account/{accountId:Guid}/limits/{organizationId:int}")]
        public async Task<ActionResult<ApiDataResult<AccountLimitDTO>>> GetLimitsByAccountId([FromRoute] Guid accountId)
        {
            var resourceLimit = await _adaptiveCloudService.GetLimitByAccountId(accountId);

            return new ApiDataResult<AccountLimitDTO>
            {
                Data = resourceLimit
            };
        }

        [OrgAuthorize(Perms.ManageCloudInfraLimits)]
        [HttpPost("organization/{organizationId:int}/limits/remove")]
        public async Task<ActionResult<ApiResult>> RemoveLimits([FromRoute] int organizationId)
        {
            await _adaptiveCloudService.RemoveAccountLimit(organizationId);

            return new ApiResult
            {
                Message = "Limits removed successfully."
            };
        }
    }
}