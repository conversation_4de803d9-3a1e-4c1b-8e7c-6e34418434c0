import { inject, Injectable } from '@angular/core';
import { CloudInfraParamsEnum } from '@app/shared/models/cloud-infra/params.enum';
import { VmInstance } from '@app/shared/models/cloud-infra/vm-instance.model';
import { CloudInfrastructureApiService } from '@app/shared/services/cloud-infrastructure-api.service';
import { forkJoin, map, Observable, of, switchMap } from 'rxjs';
import { NameIdModel } from '../models/name-id.model';
import { Snapshot } from '../models/snapshot';
import { SSHKeyPair } from '../models/ssh-key-pair';
import { VIRTUAL_MACHINES_ENDPOINT_NAMES } from '../models/vm.constants';
import { DestroyVm } from '../responses/destroy-vm.response';
import { ExpungeVm } from '../responses/expunge-vm.response';
import { ListClusterResponse } from '../responses/list-cluster.response';
import { FindHostsForMigrationResponse, ListFindHostsForMigration } from '../responses/list-host-for-migration.response';
import { ListHostResponse } from '../responses/list-host.response';
import { ListKeyPairResponse } from '../responses/list-key-pair.response';
import { ListPodResponse } from '../responses/list-pod.response';
import { ListVMSnapshotResponse } from '../responses/list-vm-snapshot.response';
import { ListVm, ListVmResponse } from '../responses/list-vm.response';
import { MigrateVmHostResponse } from '../responses/migrate-vm-host.response';
import { RebootVm } from '../responses/reboot-vm.response';
import { RecoverVm } from '../responses/recover-vm.response';
import { ReinstallVmResponse } from '../responses/reinstall-vm.response';
import { ResetPasswordResponse } from '../responses/reset-password.response';
import { ResetSSHKeyPairResponse } from '../responses/reset-ssh-key-pair.response';
import { SnapshotVmResponse } from '../responses/snapshot-vm.response';
import { SnapshotVolumeResponse } from '../responses/snapshot-volume.response';
import { StartVm } from '../responses/start-vm.response';
import { StopVm } from '../responses/stop-vm.response';
import { CreateSnapshotFromVMSnapshotResponse } from '../responses/create-snapshot-from-vm-snapshot.response';
import { AsyncJobResponse } from '../responses/async-job.response';
import { VolumeType } from '@app/modules/cloud-infrastructure/models/volumes/volume-type';
import { ListVolumeResponse } from '@app/modules/cloud-infrastructure/responses/list-volume.response';

@Injectable({
    providedIn: 'root'
})
export class VmManagementService {

    private readonly cloudInfraApiService = inject(CloudInfrastructureApiService);

    /*
    * This method retrieves a list of virtual machines for a given domain and account.
    * The domainId argument is required and it expects either the domainId in the cloud infra user context.
    * The account argument is optional and it should only be used when the context does not have its own domain.
    */
    getVirtualMachineList(domainId: string, account: string | null): Observable<VmInstance[]> {
        const pageSize = 500;

        // Fetch the first batch, which will return the total count, then fetch the rest of the records in parallel
        return this.getVirtualMachineListBatch(domainId, account, 1, pageSize).pipe(
            map(res => {
                const records = [...res?.virtualmachine ?? []];
                const remainingRecords = res.count - pageSize;

                if (remainingRecords > 0) {
                    const countOfRequestBatches = Math.ceil(remainingRecords / pageSize);
                    const requests: Observable<ListVm>[] = [];
                    for (let i = 2; i <= countOfRequestBatches + 1; i++) {
                        requests.push(this.getVirtualMachineListBatch(domainId, account, i, pageSize));
                    }
                    return forkJoin(requests).pipe(map(responses => {
                        responses.forEach(response => {
                            records.push(...response.virtualmachine);
                        });
                        return records;
                    }));
                }
                return of(records);

            }),
            switchMap(records => records)
        );
    }

    private getVirtualMachineListBatch(domainId: string, account: string | null, currentPage: number, pageSize: number): Observable<ListVm> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listVirtualMachines,
            details: 'all', // 'all' is required because includes details such as ISO / template
            isrecursive: account ? 'false' : 'true',
            listall: 'true',
            pagesize: pageSize.toString(),
            page: currentPage.toString()
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;

        if (account) {
            params[CloudInfraParamsEnum.ACCOUNT] = account;
        }

        return this.cloudInfraApiService.get<ListVmResponse>(params)
            .pipe(map((response: ListVmResponse) => response.listvirtualmachinesresponse));
    }

    getVirtualMachineListByIds(ids: string[]): Observable<VmInstance[]> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listVirtualMachines,
            details: 'all', // 'all' is required because includes details such as ISO / template
            isrecursive: 'true',
            listall: 'true',
            ids: ids.join(',')
        };

        return this.cloudInfraApiService.get<ListVmResponse>(params)
            .pipe(map((response: ListVmResponse) => response.listvirtualmachinesresponse.virtualmachine));
    }

    getVolumesByVirtualMachine(virtualMachineId: string, domainId: string, account: string, volumeType?: VolumeType): Observable<NameIdModel[]> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listVolumes,
            details: 'min',
            listall: 'true',
            virtualmachineid: virtualMachineId
        };

        if (volumeType) {
            params['type'] = volumeType.toString();
        }

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<ListVolumeResponse>(params)
            .pipe(map((response: ListVolumeResponse) => response.listvolumesresponse?.volume?.map(volume => ({ name: volume.name, id: volume.id }))));
    }

    getPodList(zoneId: string): Observable<NameIdModel[]> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listPods,
            zoneid: zoneId
        };

        return this.cloudInfraApiService.get<ListPodResponse>(params)
            .pipe(map(response => response.listpodsresponse?.pod.map(pod => ({ name: pod.name, id: pod.id })).sort((a, b) => a.name.localeCompare(b.name))));
    }

    getClusterList(zoneId: string): Observable<NameIdModel[]> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listClusters,
            zoneid: zoneId
        };

        return this.cloudInfraApiService.get<ListClusterResponse>(params)
            .pipe(map(response => response.listclustersresponse?.cluster.map(pod => ({ name: pod.name, id: pod.id })).sort((a, b) => a.name.localeCompare(b.name))));
    }

    getHostList(zoneId: string, type: string, state: string): Observable<NameIdModel[]> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listHosts,
            zoneid: zoneId
        };

        if (type) {
            params['type'] = type;
        }

        if (state) {
            params['state'] = state;
        }

        return this.cloudInfraApiService.get<ListHostResponse>(params)
            .pipe(map(response => response.listhostsresponse?.host.map(pod => ({ name: pod.name, id: pod.id })).sort((a, b) => a.name.localeCompare(b.name))));
    }

    getKeyPairList(domainId: string, account: string): Observable<SSHKeyPair[]> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listSSHKeyPairs,
            listall: 'true'
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<ListKeyPairResponse>(params)
            .pipe(map(response => response.listsshkeypairsresponse?.sshkeypair));
    }

    getConsoleUrl(virtualMachineId: string): Observable<string> {
        const params: Record<string, string> = {
            vm: virtualMachineId,
            cmd: 'access'
        };

        return this.cloudInfraApiService.getConsoleUrl(params);
    }

    getVirtualMachineSnapshots(virtualMachineId: string): Observable<Snapshot[]> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.listVirtualMachineSnapshots,
            listall: 'true',
            details: 'min',
            virtualmachineid: virtualMachineId
        };

        return this.cloudInfraApiService.get<ListVMSnapshotResponse>(params)
            .pipe(map(response => response.listvmsnapshotresponse?.vmSnapshot ?? []));

    }

    stopVirtualMachine(id: string, forceStop: boolean): Observable<string> {
        const params: Record<string, string> = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.stopVirtualMachine,
            id,
            forced: forceStop ? 'true' : 'false'
        };

        return this.cloudInfraApiService.get<StopVm>(params)
            .pipe(map(response => response.stopvirtualmachineresponse?.jobid));
    }

    destroyVirtualMachine(id: string, expunge: boolean, volumesToDelete: string[]): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.destroyVirtualMachine,
            id,
            expunge: expunge ? 'true' : 'false'
        };

        if (volumesToDelete.length > 0) {
            params['volumeids'] = volumesToDelete.toString();
        }

        return this.cloudInfraApiService.get<DestroyVm>(params).pipe(map(response => response.destroyvirtualmachineresponse?.jobid));
    }

    rebootVirtualMachine(id: string, bootDelay: number): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.rebootVirtualMachine,
            id
        };

        if (bootDelay) {
            params['bootdelay'] = bootDelay.toString();
        }

        return this.cloudInfraApiService.get<RebootVm>(params).pipe(map(response => response.rebootvirtualmachineresponse?.jobid));
    }

    startVirtualMachine(id: string, podId: string | null, clusterId: string | null, hostId: string | null, bootDelay: number | null): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.startVirtualMachine,
            id
        };

        if (podId) {
            params['podid'] = podId;
        }

        if (clusterId) {
            params['clusterid'] = clusterId;
        }

        if (hostId) {
            params['hostid'] = hostId;
        }

        if (bootDelay) {
            params['bootdelay'] = bootDelay?.toString();
        }

        return this.cloudInfraApiService.get<StartVm>(params).pipe(map(response => response.startvirtualmachineresponse?.jobid));
    }

    resetSSHKeyPairForVirtualMachine(id: string, keyPair: string, domainId: string, account: string): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.resetSSHKeyForVirtualMachine,
            id,
            keypair: keyPair
        };

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<ResetSSHKeyPairResponse>(params).pipe(map(response => response.resetSSHKeyforvirtualmachineresponse?.jobid));
    }

    snapshotVirtualMachine(id: string, name: string, description: string, snapshotMemory: boolean): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.snapshotVirtualMachine,
            virtualmachineid: id,
            snapshotmemory: snapshotMemory ? 'true' : 'false'
        };

        if (name) {
            params['name'] = name;
        }

        if (description) {
            params['description'] = description;
        }

        return this.cloudInfraApiService.get<SnapshotVmResponse>(params).pipe(map(response => response.createvmsnapshotresponse?.jobid));
    }

    snapshotVolume(volumeId: string, asyncBackup: boolean, snapshotName: string, domainId: string, account: string): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.snapshotVolume,
            volumeid: volumeId,
            asyncbackup: asyncBackup ? 'true' : 'false'
        };

        if (snapshotName) {
            params['name'] = snapshotName;
        }

        params[CloudInfraParamsEnum.DOMAIN_ID] = domainId;
        params[CloudInfraParamsEnum.ACCOUNT] = account;

        return this.cloudInfraApiService.get<SnapshotVolumeResponse>(params).pipe(map(response => response.createsnapshotresponse?.jobid));
    }

    expungeVirtualMachine(id: string): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.expungeVirtualMachine,
            id
        };

        return this.cloudInfraApiService.get<ExpungeVm>(params).pipe(map(response => response.expungevirtualmachineresponse?.jobid));
    }

    recoverVirtualMachine(id: string): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.recoverVirtualMachine,
            id
        };

        return this.cloudInfraApiService.get<RecoverVm>(params).pipe(map(response => response.recovervirtualmachineresponse?.jobid));
    }

    findHostsForMigration(virtualMachineId: string): Observable<ListFindHostsForMigration> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.findHostsForMigration,
            virtualmachineid: virtualMachineId,
        };
        return this.cloudInfraApiService.get<FindHostsForMigrationResponse>(params).pipe(map((response => response.findhostsformigrationresponse)));
    }

    migrateVirtualMachineHost(id: string, hostId: string): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.migrateVirtualMachine,
            virtualmachineid: id,
            hostid: hostId
        };
        return this.cloudInfraApiService.get<MigrateVmHostResponse>(params).pipe(map(response => response.migratevirtualmachineresponse?.jobid));
    }

    reinstallVirtualMachine(id: string, mediaId: string | null): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.reinstallVirtualMachine,
            virtualmachineid: id
        };

        if (mediaId) {
            params['templateid'] = mediaId;
        }

        return this.cloudInfraApiService.get<ReinstallVmResponse>(params).pipe(map(response => response.restorevmresponse?.jobid));
    }

    resetVirtualMachinePassword(id: string): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.resetVirtualMachinePassword,
            id
        };

        return this.cloudInfraApiService.get<ResetPasswordResponse>(params).pipe(map(response => response.resetpasswordforvirtualmachineresponse?.jobid));
    }

    createSnapshotFromVirtualMachineSnapshot(virtualMachineSnapshotId: string, volumeId: string, name: string): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.createSnapshotFromVirtualMachineSnapshot,
            vmsnapshotid: virtualMachineSnapshotId,
            volumeid: volumeId,
            name
        };

        return this.cloudInfraApiService.get<CreateSnapshotFromVMSnapshotResponse>(params).pipe(map(response => response.createsnapshotfromvmsnapshotresponse?.jobid));
    }

    deleteVirtualMachineSnapshot(snapshotId: string): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.deleteVirtualMachineSnapshot,
            vmsnapshotid: snapshotId
        };

        return this.cloudInfraApiService.get<AsyncJobResponse>(params).pipe(map(response => response?.jobid));
    }

    revertToVirtualMachineSnapshot(snapshotId: string): Observable<string> {
        const params = {
            command: VIRTUAL_MACHINES_ENDPOINT_NAMES.revertToVirtualMachineSnapshot,
            vmsnapshotid: snapshotId
        };

        return this.cloudInfraApiService.get<AsyncJobResponse>(params).pipe(map(response => response?.jobid));
    }

}
