import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { RouterLink, RouterLinkActive, RouterOutlet } from '@angular/router';
import { DomainAccountTreeComponent } from '@app/modules/cloud-infrastructure/components/domain-account-tree/domain-account-tree.component';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { STORAGE_ROUTE_SEGMENTS } from '../../models/route.segments';
import { StoragePermissionService } from '../../services/storage-permission.service';

@Component({
    selector: 'app-storage-management',
    imports: [RouterLink, RouterOutlet, RouterLinkActive, DomainAccountTreeComponent],
    templateUrl: './storage-management.component.html',
    styleUrl: './storage-management.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class StorageManagementComponent {
    protected readonly store = inject(ZoneDomainAccountStore);
    protected readonly storagePermissionService = inject(StoragePermissionService);

    protected readonly routes = STORAGE_ROUTE_SEGMENTS;
}

