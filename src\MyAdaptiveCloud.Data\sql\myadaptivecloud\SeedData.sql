-- liquibase formatted sql

-- changeset jbarrera:F21FC831-3B55-4488-B831-69122803E14D runOnChange:true context:dev,test
-- Configurations
CALL AddConfigurationCategory('website');
CALL AddConfigurationCategory('SMTP');
CALL AddConfigurationCategory('WhiteLabel');
CALL AddConfigurationCategory('KeyCloak');
CALL AddConfigurationCategory('Cookies');
CALL AddConfigurationCategory('Home Screen');
CALL AddConfigurationCategory('AdaptiveCloud Api');
CALL AddConfigurationCategory('ConnectWise Api');
CALL AddConfigurationCategory('Cloud Infrastructure');
CALL AddConfigurationCategory('PowerDNS');
CALL AddConfigurationCategory('Agent');
CALL AddConfigurationCategory('Cloud Infrastructure Initial Limits');
CALL AddConfigurationCategory('Cloud Infrastructure Reconcile');
CALL AddConfigurationCategory('Feature Flags');

-- changeset jbarrera:DE953478-8CB6-44CF-93A2-6E7E47EA4887 runOnChange:true context:dev,test
-- Configuration Values
CALL SetConfigurationValue('SMTP','smtpServer','mail.ippathways.com','input', 0);
CALL SetConfigurationValue('SMTP','smtpEmail','<EMAIL>','input', 0);
CALL SetConfigurationValue('SMTP','smtpPort','25','input', 0);
CALL SetConfigurationValue('SMTP','smtpUserName','mail.ippathways.com','input', 0);
CALL SetConfigurationValue('SMTP','RegistrationEmail ','<EMAIL>','input', 0);
CALL SetConfigurationValue('SMTP','smtpWorkerDelay','120000','input', 0);
CALL SetConfigurationValue('SMTP','smtpSenderEmail','<EMAIL>','input', 0);
CALL SetConfigurationValue('WhiteLabel','Default','1','input', 0);
CALL SetConfigurationValue('Cookies','ExpiryHours','20','input', 0);
CALL SetConfigurationValue('Home Screen','Heading','','input', 0);
CALL SetConfigurationValue('ConnectWise Api','CWClientId','02305903-bffb-4a31-993a-29959d52c598','input', 0);
CALL SetConfigurationValue('ConnectWise Api','CWCompany','ippathwaystest','input', 0);
CALL SetConfigurationValue('ConnectWise Api','CWPublicKey','7qEF5w9ITVWtyJln','input', 0);
CALL SetConfigurationValue('ConnectWise Api','Partner Type','Partner','input', 0);
CALL SetConfigurationValue('ConnectWise Api','Customer Type','Customer','input', 0);
CALL SetConfigurationValue('ConnectWise Api', 'Invoice Query Months', '12', 'input', 0);
CALL SetConfigurationValue('Cloud Infrastructure', 'UserRole', 'Cloud Infra User', 'input', 0);
CALL SetConfigurationValue('Cloud Infrastructure','Allowed Domains','adaptivecloud.com,ippathways.com,ippathways.us','input', 0);
CALL SetConfigurationValue('PowerDNS','PDNSDefaultTTL','86400','input', 0);

CALL SetConfigurationValue('Cloud Infrastructure Initial Limits','Instances','2','input', 0);
CALL SetConfigurationValue('Cloud Infrastructure Initial Limits','PublicIp','1','input', 0);
CALL SetConfigurationValue('Cloud Infrastructure Initial Limits','Cpu','8','input', 0);
CALL SetConfigurationValue('Cloud Infrastructure Initial Limits','Memory','16000','input', 0);
CALL SetConfigurationValue('Cloud Infrastructure Initial Limits','PrimaryStorage','500','input', 0);
CALL SetConfigurationValue('Cloud Infrastructure Initial Limits','SecondaryStorage','500','input', 0);
CALL SetConfigurationValue('Cloud Infrastructure Reconcile', 'Enabled', 'true', 'input', 0);
CALL SetConfigurationValue('Cloud Infrastructure Reconcile', 'EnableRenaming', 'true', 'input', 0);

-- changeset jbarrera:5B9264A6-9093-4747-B4FF-DB1D8834D441 runOnChange:true context:dev,test
-- Local only configuration values
CALL SetConfigurationValue('AdaptiveCloud Api','CSApiKey','ntnX7tBHLnMGAKLgN6TAKL55JQlU_HoQl6zKtKORa-q2g7UINGamEAtGSeHtXe_Af8rEtO5cV0JBS5SFECmKhA','input', 0);
CALL SetConfigurationValue('AdaptiveCloud Api','CSBaseUrl','https://labcloud01.ippathways.us/client/api','input', 0);
CALL SetConfigurationValue('ConnectWise Api','CWBaseUrl','https://connect-test.ippathways.com/v4_6_release/apis/3.0','input', 0);
CALL SetConfigurationValue('PowerDNS','PDNSBaseUrl','http://ns1.dsm1.ippathways.net:8081','input', 0);
CALL SetConfigurationValue('KeyCloak','ClientId','myadaptivecloudoidc','input', 0);
CALL SetConfigurationValue('KeyCloak','Authority','https://sso.adaptivecloud.com/auth/realms/Test','input', 0);

-- changeset jbarrera:3F989CF4-7D73-41CA-8357-076DBC1A66FE runOnChange:true context:dev
CALL SetConfigurationValue('SMTP','smtpSendApproval','false','input', 0);

-- changeset jbarrera:A36615E2-827C-429D-9532-3E8D88C0E336 runOnChange:true context:test
CALL SetConfigurationValue('SMTP','smtpSendApproval','true','input', 0);

-- changeset jbarrera:04D47B10-4B9A-4A91-9E54-455F30998483 runOnChange:true context:dev,test endDelimiter:|
IF NOT EXISTS (SELECT 1 FROM ConfigurationValues cv INNER JOIN Configuration c ON cv.ConfigurationId = c.ConfigurationId WHERE c.Category = 'KeyCloak' AND cv.Name = 'ClientSecret') THEN
    CALL SetConfigurationValue('KeyCloak','ClientSecret','','input', 1);    
ELSE 
    SET @configurationId = (SELECT cv.ConfigurationValuesId FROM ConfigurationValues cv INNER JOIN Configuration c ON cv.ConfigurationId = c.ConfigurationId WHERE c.Category = 'KeyCloak' AND cv.Name = 'ClientSecret');
    UPDATE ConfigurationValues SET IsSecret = 1 WHERE ConfigurationValuesId = @configurationId;
END IF|

-- changeset jbarrera:47B97773-F499-4F20-A151-6FEDE3A36A97 runOnChange:true context:dev,test endDelimiter:|
IF NOT EXISTS (SELECT 1 FROM ConfigurationValues cv INNER JOIN Configuration c ON cv.ConfigurationId = c.ConfigurationId WHERE c.Category = 'SMTP' AND cv.Name = 'smtpPassword') THEN
    CALL SetConfigurationValue('SMTP','smtpPassword','','input', 0);
END IF|

-- changeset jbarrera:42066E71-55D0-4F32-BE67-6DE4EE683089 runOnChange:true context:dev,test endDelimiter:|
IF NOT EXISTS (SELECT 1 FROM ConfigurationValues cv INNER JOIN Configuration c ON cv.ConfigurationId = c.ConfigurationId WHERE c.Category = 'PowerDNS' AND cv.Name = 'PDNSApiKey') THEN
    CALL SetConfigurationValue('PowerDNS','PDNSApiKey','','input', 1);
END IF|

-- changeset jbarrera:39142ED9-0633-454D-8D87-17FE5F417FDD runOnChange:true context:dev,test endDelimiter:|
IF NOT EXISTS (SELECT 1 FROM ConfigurationValues cv INNER JOIN Configuration c ON cv.ConfigurationId = c.ConfigurationId WHERE c.Category = 'ConnectWise Api' AND cv.Name = 'CWPrivateKey') THEN
    CALL SetConfigurationValue('ConnectWise Api','CWPrivateKey','','input',1);
END IF|

-- changeset jbarrera:E5F99AC3-C2BC-4005-BD98-FAA4E682BC3C runOnChange:true context:dev,test endDelimiter:|
IF NOT EXISTS (SELECT 1 FROM ConfigurationValues cv INNER JOIN Configuration c ON cv.ConfigurationId = c.ConfigurationId WHERE c.Category = 'AdaptiveCloud Api' AND cv.Name = 'CSSecretKey') THEN
    CALL SetConfigurationValue('AdaptiveCloud Api','CSSecretKey','','input', 1);
END IF|
    
-- changeset jbarrera:22518BA9-C2D5-464A-B385-790B2EACB515 runOnChange:true context:dev,test  endDelimiter:|
IF NOT EXISTS (SELECT 1 FROM Organization WHERE OrganizationId = 0 AND Name = 'Root') THEN
    INSERT INTO Organization (Name, CreatedDate, IsActive, AllowSubOrg, AllowWhiteLabel, IsPartner, IsVerified, ParentOrganizationId) 
    VALUES ('Root', NOW(), 1, 1, 1, 0, 1, null);
    UPDATE Organization SET OrganizationId = 0 WHERE OrganizationId = LAST_INSERT_ID() AND Name = 'Root';
END IF|

-- changeset jbarrera:FEF35A2D-1686-4E6C-8388-0A96787EB0EC runOnChange:true context:dev,test
-- Create Default White Label
INSERT IGNORE INTO `WhiteLabel` (`WhiteLabelId`, `OrganizationId`, `PortalName`, `DomainName`, `PrimaryColor`, `SecondaryColor`, `CreatedDate`, `IsActive`, `AdaptiveCloudHostname`) 
VALUES (1, 0, 'MyAdaptiveCloud', 'my.adaptivecloud.com', '#3079A7', '#666666', now(), 1, 'localhost');

-- changeset jbarrera:3F11028B-BBF7-4549-9850-7FEF3FEAE825 runOnChange:true context:dev,test
-- Create default roles
INSERT IGNORE INTO `Role` (`RoleId`, `Name`, `OrganizationId`, `Permissions`, `AvailableTo`, `IsRestricted`, `Description`) 
VALUES (1, 'Super Admin', 0, 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF, 1, 1, 'Super Admin');
INSERT IGNORE INTO `Role` (`RoleId`, `Name`, `OrganizationId`, `Permissions`, `AvailableTo`, `IsRestricted`, `Description`) 
VALUES (2, 'Admin', 0, 0xFF7F7EE881160000000000000000000000000000, 1, 0, 'Admin');
INSERT IGNORE INTO `Role` (`RoleId`, `Name`, `OrganizationId`, `Permissions`, `AvailableTo`, `IsRestricted`, `Description`)
VALUES (3, 'User', 0, 0xFFFFFF1F00000000000000000000000000000000, 1, 0, 'User');

-- changeset jbarrera:E5515F5A-96CE-4399-A155-0D57FE35B4F1 runOnChange:true context:dev,test
-- Create local admin user
INSERT IGNORE INTO `User` (`FirstName`, `Email`, `LastName`, `Password`, `CreatedDate`) 
VALUES ('Local Admin', '<EMAIL>', 'Local Admin', '$2a$11$MXMwGFZ.spue6PHWTxn6YOxPvrqrbrhQU33/uExsWO6clWNKI6s/y', now());

-- changeset ggoodrich:B702813B-9B94-4DCE-824B-17E7575346A7 runOnChange:true endDelimiter:|
IF NOT EXISTS (SELECT 1 FROM Role WHERE Name = 'Cloud Infra User') THEN
    -- Insert 'Cloud Infra User' role if it doesn't exist
    INSERT INTO Role (Name, organizationId, Permissions, AvailableTo, IsRestricted, Description)
    VALUES('Cloud Infra User', 0, 0x01D0170000000000000000000000000000000000, 3, 0, 'General user with Cloud Infrastructure and no admin rights');
END IF|

-- changeset voviedo:6cc2375b-32c0-4450-ad9f-69c72134d674
INSERT IGNORE INTO `device_folder` (`FolderId`, `ParentFolderId`, `OrganizationId`, `Name`) VALUES (291, NULL, 291, 'Updates Folder');
INSERT IGNORE INTO `device_folder` (`FolderId`, `ParentFolderId`, `OrganizationId`, `Name`) VALUES (162, NULL, 162, 'Gorilla Test ID FOLDER');
INSERT IGNORE INTO `device_folder` (`FolderId`, `ParentFolderId`, `OrganizationId`, `Name`) VALUES (162, NULL, 163, 'Gorilla NOT Test ID FOLDER');
INSERT IGNORE INTO `device_folder` (`FolderId`, `ParentFolderId`, `OrganizationId`, `Name`) VALUES (311, NULL, 311, 'Alex Test ID FOLDER');
INSERT IGNORE INTO `device_folder` (`FolderId`, `ParentFolderId`, `OrganizationId`, `Name`) VALUES (312, NULL, 311, 'Alex NOT Test ID FOLDER');

-- changeset jbarrera:3EE46E80-CDA5-4ED8-9E2D-D6EC678EA09B context:dev,test
INSERT IGNORE INTO `Organization` (`OrganizationId`, `Name`, `CreatedDate`, `UpdatedDate`, 
`IsActive`, `CreatedBy`, `UpdatedBy`, `AllowSubOrg`, `AllowWhiteLabel`, 
`ParentOrganizationId`, `IsPartner`, `IsVerified`) VALUES 
(291, 'Nates Testing Org', '2022-10-05 15:01:46.710641', NULL, 1, NULL, NULL, 1, 1, 1, 1, 1);

-- changeset voviedo:B063C038-EA8E-4778-AE29-B66C61DD544B context:dev,test
INSERT IGNORE INTO `OrganizationMapping` (`OrganizationId`, `Application`, `PrimaryId`, 
`PrimaryName`, `SecondaryId`, `SecondaryName`, `CreatedBy`, `UpdatedBy`) VALUES
(291, 'BillingDB', '291', 'NateCSN', NULL, NULL, NULL, NULL);

-- changeset voviedo:a4f637e8-7705-49a2-8a77-a57308c197ab
INSERT IGNORE INTO `device_folder` (`FolderId`, `ParentFolderId`, `OrganizationId`, `Name`) VALUES (292, NULL, 291, 'Folder Alpha');
INSERT IGNORE INTO `device_folder` (`FolderId`, `ParentFolderId`, `OrganizationId`, `Name`) VALUES (293, NULL, 291, 'Folder Beta');
INSERT IGNORE INTO `device_folder` (`FolderId`, `ParentFolderId`, `OrganizationId`, `Name`) VALUES (294, NULL, 291, 'Edit Name Folder');
INSERT IGNORE INTO `device_folder` (`FolderId`, `ParentFolderId`, `OrganizationId`, `Name`) VALUES (295, 293, 291, 'Folder Gamma');