import { ComponentFixture, fakeAsync, flush, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgSelectComponent } from '@ng-select/ng-select';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NetworkingService } from '../../services/networking.service';
import { NetworkingDetailService } from '../../services/networking-detail.service';
import { CreateNetworkService } from '@app/modules/cloud-infrastructure/services/create-network.service';
import { NetworkDetailComponent } from './network-detail.component';
import { of } from 'rxjs';
import { DestroyRef } from '@angular/core';
import { By } from '@angular/platform-browser';
import { selectOption } from '@app/shared/test-helper/testng-select';

describe('NetworkDetailComponent', () => {
    let component: NetworkDetailComponent;
    let fixture: ComponentFixture<NetworkDetailComponent>;
    let mockNetworkingService: jasmine.SpyObj<NetworkingService>;
    let mockNetworkingDetailService: NetworkingDetailService;
    let mockCreateNetworkService: jasmine.SpyObj<CreateNetworkService>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                NetworkDetailComponent,
                ReactiveFormsModule,
                BtnSubmitComponent,
                NgSelectComponent
            ],
            providers: [
                provideMock(NetworkingService),
                NetworkingDetailService,
                provideMock(CreateNetworkService),
                FormBuilder,
                DestroyRef
            ]
        });

        mockNetworkingService = TestBed.inject(NetworkingService) as jasmine.SpyObj<NetworkingService>;
        mockNetworkingDetailService = TestBed.inject(NetworkingDetailService);
        mockCreateNetworkService = TestBed.inject(CreateNetworkService) as jasmine.SpyObj<CreateNetworkService>;
        mockNetworkingDetailService.selectedNetwork.set({
            id: 'network-id',
            name: 'Test Network',
            displaytext: 'Test Network Description',
            cidr: '***********/24',
            networkdomain: 'test-domain',
            networkofferingid: 'offering-id',
            zonename: 'Test Zone',
            domain: 'Test Domain',
            account: 'test-account',
            type: 'Isolated',
            restartrequired: true,
            redundantrouter: false,
            acltype: '',
            canusefordeploy: false,
            created: '',
            displaynetwork: false,
            dns1: '',
            dns2: '',
            domainid: '',
            hasannotations: false,
            ip6dns1: '',
            ip6dns2: '',
            ispersistent: false,
            issystem: false,
            networkofferingavailability: '',
            networkofferingconservemode: false,
            networkofferingdisplaytext: '',
            networkofferingname: '',
            physicalnetworkid: '',
            privatemtu: 0,
            publicmtu: 0,
            receivedbytes: 0,
            related: '',
            sentbytes: 0,
            specifyipranges: false,
            state: 'Implemented',
            strechedl2subnet: false,
            supportsvmautoscaling: false,
            tags: [],
            traffictype: '',
            zoneid: ''
        });

        mockCreateNetworkService.getNetworkOfferings.and.returnValue(of([{name: 'Offering 1', id: 'Offering 1', forVPC: false, specifyVLan: false}, {name: 'Offering 2', id: '2', forVPC: true, specifyVLan: true}]));

        mockNetworkingService.editNetwork.and.returnValue(of('job-id'));

        fixture = TestBed.createComponent(NetworkDetailComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize form with selected network values', () => {
        const nameInput = fixture.nativeElement.querySelector('input[formControlName="name"]');
        const descriptionInput = fixture.nativeElement.querySelector('input[formControlName="description"]');
        const cidrInput = fixture.nativeElement.querySelector('input[formControlName="cidr"]');
        const networkdomainInput = fixture.nativeElement.querySelector('input[formControlName="networkDomain"]');
        expect(nameInput.value).toBe('Test Network');
        expect(descriptionInput.value).toBe('Test Network Description');
        expect(cidrInput.value).toBe('***********/24');
        expect(networkdomainInput.value).toBe('test-domain');
    });

    it('should load network offerings', () => {
        expect(mockCreateNetworkService.getNetworkOfferings).toHaveBeenCalled();
    });

    it('should enable controls in edit mode', () => {
        mockNetworkingDetailService.isEditMode.set(true);
        fixture.detectChanges();
        const nameInput = fixture.nativeElement.querySelector('input[formControlName="name"]');
        const descriptionInput = fixture.nativeElement.querySelector('input[formControlName="description"]');
        expect(nameInput.disabled).toBeFalse();
        expect(descriptionInput.disabled).toBeFalse();
    });

    it('should disable controls in view mode', () => {
        mockNetworkingDetailService.isEditMode.set(false);
        fixture.detectChanges();
        const nameInput = fixture.nativeElement.querySelector('input[formControlName="name"]');
        const descriptionInput = fixture.nativeElement.querySelector('input[formControlName="description"]');
        expect(nameInput.disabled).toBeTrue();
        expect(descriptionInput.disabled).toBeTrue();
    });

    it('should submit valid form', fakeAsync(() => {
        mockNetworkingDetailService.isEditMode.set(true);
        fixture.detectChanges();
        const nameInput = fixture.nativeElement.querySelector('input[formControlName="name"]');
        const descriptionInput = fixture.nativeElement.querySelector('input[formControlName="description"]');
        const cidrInput = fixture.nativeElement.querySelector('input[formControlName="cidr"]');
        const networkdomainInput = fixture.nativeElement.querySelector('input[formControlName="networkDomain"]');

        nameInput.value = 'Updated Network';
        nameInput.dispatchEvent(new Event('input'));
        descriptionInput.value = 'Updated Description';
        descriptionInput.dispatchEvent(new Event('input'));
        cidrInput.value = '***********/24';
        cidrInput.dispatchEvent(new Event('input'));
        networkdomainInput.value = 'updated-domain';
        networkdomainInput.dispatchEvent(new Event('input'));
        selectOption(fixture, 'ng-select', 1, true, 0);

        fixture.detectChanges();
        const submitBtn = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
        submitBtn.click();
        fixture.detectChanges();
        flush();

        expect(mockNetworkingService.editNetwork).toHaveBeenCalledOnceWith(
            'network-id',
            'Updated Network',
            'Updated Description',
            '***********/24',
            'updated-domain',
            '2'
        );
    }));

    it('should not submit when name is invalid', () => {
        mockNetworkingDetailService.isEditMode.set(true);
        fixture.detectChanges();
        const nameInput = fixture.nativeElement.querySelector('input[formControlName="name"]');
        nameInput.value = '';
        nameInput.dispatchEvent(new Event('input'));
        fixture.detectChanges();

        const submitBtn = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
        submitBtn.click();
        fixture.detectChanges();

        expect(mockNetworkingService.editNetwork).not.toHaveBeenCalled();
    });

    it('should not submit when description is invalid', () => {
        mockNetworkingDetailService.isEditMode.set(true);
        fixture.detectChanges();
        const descriptionInput = fixture.nativeElement.querySelector('input[formControlName="description"]');
        descriptionInput.value = '';
        descriptionInput.dispatchEvent(new Event('input'));
        fixture.detectChanges();
        const submitBtn = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
        submitBtn.click();
        fixture.detectChanges();

        expect(mockNetworkingService.editNetwork).not.toHaveBeenCalled();
    });

    it('should not submit when cidr is invalid', () => {
        mockNetworkingDetailService.isEditMode.set(true);
        fixture.detectChanges();
        const cidrInput = fixture.nativeElement.querySelector('input[formControlName="cidr"]');
        cidrInput.value = '';
        cidrInput.dispatchEvent(new Event('input'));
        fixture.detectChanges();
        const submitBtn = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
        submitBtn.click();
        fixture.detectChanges();

        expect(mockNetworkingService.editNetwork).not.toHaveBeenCalled();
    });

    it('should not submit when networkDomain is invalid', () => {
        mockNetworkingDetailService.isEditMode.set(true);
        fixture.detectChanges();
        const networkDomainInput = fixture.nativeElement.querySelector('input[formControlName="networkDomain"]');
        networkDomainInput.value = '';
        networkDomainInput.dispatchEvent(new Event('input'));
        fixture.detectChanges();
        const submitBtn = fixture.debugElement.query(By.directive(BtnSubmitComponent)).query(By.css('button')).nativeElement;
        submitBtn.click();
        fixture.detectChanges();

        expect(mockNetworkingService.editNetwork).not.toHaveBeenCalled();
    });
});
