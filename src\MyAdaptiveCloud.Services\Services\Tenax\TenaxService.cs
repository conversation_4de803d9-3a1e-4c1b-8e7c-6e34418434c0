﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Contexts;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.OrganizationMappings;
using MyAdaptiveCloud.Data.Repositories;
using MyAdaptiveCloud.Services.Apis.Tenax;
using MyAdaptiveCloud.Services.Apis.Tenax.Model;
using MyAdaptiveCloud.Services.DTOs.Person;
using MyAdaptiveCloud.Services.DTOs.Tenax;
using MyAdaptiveCloud.Services.Exceptions;
using MyAdaptiveCloud.Services.Requests.Tenax;

namespace MyAdaptiveCloud.Services.Services.Tenax
{
    public class TenaxService : ITenaxService
    {
        private readonly IOrganizationMappingService _organizationMappingService;
        private readonly IOrganizationService _organizationService;
        private readonly IOrganizationMappingRepository _organizationMappingRepository;
        private readonly MyAdaptiveCloudContext _dbContext;
        private readonly ITenaxApi _tenaxApi;
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly ILogger<ITenaxService> _logger;
        private readonly string badRequestMessage = "Tenant does not exist.";

        public TenaxService(
            ITenaxApi tenaxApi,
            IOrganizationMappingService organizationMappingService,
            IOrganizationService organizationService,
            IOrganizationMappingRepository organizationMappingRepository,
            MyAdaptiveCloudContext myAdaptiveCloudContext,
            IUserRepository userRepository,
            IMapper mapper,
            ILogger<ITenaxService> logger
        )
        {
            _tenaxApi = tenaxApi;
            _organizationMappingService = organizationMappingService;
            _organizationMappingRepository = organizationMappingRepository;
            _organizationService = organizationService;
            _mapper = mapper;
            _dbContext = myAdaptiveCloudContext;
            _userRepository = userRepository;
            _logger = logger;
        }

        public async Task<bool> IsProvisionedOrganization(int organizationId)
        {
            return (await _organizationMappingRepository.GetTenaxMappingByOrganization(organizationId, true)) is not null;
        }

        public async Task ProvisionOrganization(int organizationId, int currentUserId)
        {
            var isProvisioned = await IsProvisionedOrganization(organizationId);
            if (isProvisioned)
            {
                throw new BadRequestException("Organization already provisioned.");
            }

            var organization = await _organizationService.GetActiveOrganizationById(organizationId, false, true);
            var isPartner = await _organizationService.IsPartnerOrganization(organizationId);
            try
            {
                if (organization.OrganizationId == Constants.RootOrganizationId)
                {
                    var msps = await _tenaxApi.GetMSPs();
                    var firstMsp = msps[0];
                    await MapPartnerOrganization(firstMsp.Id.ToString(), organization.Name, organizationId, currentUserId);
                }
                else if (isPartner && organization.ParentOrganizationId == Constants.RootOrganizationId)
                {
                    var newTenant = await _tenaxApi.CreateMSPTenant(organization.Name);
                    await MapPartnerOrganization(newTenant.Id.ToString(), organization.Name, organizationId, currentUserId);
                }
                else if (isPartner && organization.ParentOrganizationId != Constants.RootOrganizationId)
                {
                    throw new BadRequestException("The organization can not be provisioned.");
                }
                else
                {
                    var newTenant = await _tenaxApi.CreateTenant(organization.Name);
                    await MapCustomerOrganization(newTenant.Id.ToString(), (int)organization.ParentOrganizationId, organizationId, currentUserId);
                }
            }
            catch (HttpRequestException ex)
            {
                if (ex.StatusCode == System.Net.HttpStatusCode.Conflict)
                {
                    throw new BadRequestException("The Tenant name is already in use.");
                }

                _logger.LogError(ex, "An error occurred trying to create Tenant.");
                throw new BadRequestException("An error occurred trying to create Tenant.");
            }
        }

        public async Task<Tuple<List<TenaxOrganizationDTO>, int>> GetOrganizationList(int organizationId, OrganizationsListRequest request)
        {
            var organizations = await GetSubOrganizationsDataQuery(organizationId, request.SearchTerm);
            var result = new List<TenaxOrganizationDTO>();
            var tenants = await GetTenants(true);
            var msps = await GetMSPs(true);

            foreach (var current in organizations)
            {
                var newDTO = new TenaxOrganizationDTO
                {
                    CustomerTenantId = current.CustomerTenantId,
                    Domain = null,
                    IsPartner = current.IsPartner,
                    MSPId = current.MSPId,
                    OrganizationId = current.OrganizationId.Value,
                    OrganizationName = current.OrganizationName,
                    Users = null
                };
                if (!string.IsNullOrEmpty(current.CustomerTenantId))
                {
                    var tenant = tenants.FirstOrDefault(x => x.Id == current.CustomerTenantId);
                    newDTO.Domain = tenant.Domain;
                    newDTO.Users = tenant?.Users;
                }
                else if (string.IsNullOrEmpty(current.CustomerTenantId) && !string.IsNullOrEmpty(current.MSPId))
                {
                    var tenant = msps.FirstOrDefault(x => x.Id == current.MSPId);
                    newDTO.Domain = tenant.Domain;
                    newDTO.Users = tenant?.Users;
                }

                result.Add(newDTO);
            }

            if (request.OrderBy.Equals("organizationName"))
            {
                result = request.OrderDir.Equals("asc")
                    ? result.OrderBy(x => x.OrganizationName).ToList()
                    : result.OrderByDescending(x => x.OrganizationName).ToList();
            }

            if (request.OrderBy.Equals("provision"))
            {
                result = request.OrderDir.Equals("asc")
                    ? result.OrderBy(x => x.MSPId != null || x.CustomerTenantId != null).ToList()
                    : result.OrderByDescending(x => x.MSPId != null || x.CustomerTenantId != null).ToList();
            }

            if (request.OrderBy.Equals("domain"))
            {
                result = request.OrderDir.Equals("asc")
                    ? result.OrderBy(x => x.Domain).ToList()
                    : result.OrderByDescending(x => x.Domain).ToList();
            }

            if (request.OrderBy.Equals("users"))
            {
                result = request.OrderDir.Equals("asc")
                    ? result.OrderBy(x => x.Users).ToList()
                    : result.OrderByDescending(x => x.Users).ToList();
            }

            var datacount = result.Count;
            result = result.Skip((request.CurrentPage.Value - 1) * request.PageSize.Value).Take(request.PageSize.Value).ToList();

            return new Tuple<List<TenaxOrganizationDTO>, int>(result, datacount);
        }

        public async Task<Tuple<List<TenaxUserDTO>, int>> GetUsersList(int organizationId, UsersListRequest request)
        {
            var result = await GetTenantUsers(organizationId);

            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                result = result.Where(x =>
                    x.FirstName.ToLower().Contains(request.SearchTerm.ToLower()) || x.LastName.ToLower().Contains(request.SearchTerm.ToLower()) ||
                    x.Email.ToLower().Contains(request.SearchTerm.ToLower())).ToList();
            }

            if (request.OrderBy.Equals("firstName"))
            {
                result = request.OrderDir.Equals("asc")
                    ? result.OrderBy(x => x.FirstName).ToList()
                    : result.OrderByDescending(x => x.FirstName).ToList();
            }

            if (request.OrderBy.Equals("lastName"))
            {
                result = request.OrderDir.Equals("asc")
                    ? result.OrderBy(x => x.LastName).ToList()
                    : result.OrderByDescending(x => x.LastName).ToList();
            }

            if (request.OrderBy.Equals("email"))
            {
                result = request.OrderDir.Equals("asc")
                    ? result.OrderBy(x => x.Email).ToList()
                    : result.OrderByDescending(x => x.Email).ToList();
            }

            if (request.OrderBy.Equals("lastLogin"))
            {
                result = request.OrderDir.Equals("asc")
                    ? result.OrderBy(x => x.LastLogin).ToList()
                    : result.OrderByDescending(x => x.LastLogin).ToList();
            }

            if (request.OrderBy.Equals("status"))
            {
                result = request.OrderDir.Equals("asc")
                    ? result.OrderBy(x => x.Status).ToList()
                    : result.OrderByDescending(x => x.Status).ToList();
            }

            var datacount = result.Count;
            result = result.Skip((request.CurrentPage.Value - 1) * request.PageSize.Value).Take(request.PageSize.Value).ToList();

            return new Tuple<List<TenaxUserDTO>, int>(result, datacount);
        }

        private async Task<List<TenaxUserDTO>> GetTenantUsers(int organizationId)
        {
            var mapping = await _organizationMappingRepository.GetTenaxMappingByOrganization(organizationId, true);
            if (mapping != null && mapping.CustomerTenantId != null)
            {
                var result = await _tenaxApi.GetTenantUsers(mapping.CustomerTenantId);
                return _mapper.Map<List<TenaxUserDTO>>(result.Items);
            }
            else if (mapping != null && mapping.MSPId != null)
            {
                var result = await _tenaxApi.GetMSPUsers(mapping.MSPId);
                return _mapper.Map<List<TenaxUserDTO>>(result.Items);
            }
            else
            {
                throw new BadRequestException(badRequestMessage);
            }
        }

        public async Task<List<PersonDTO>> GetAvailableUsers(int organizationId)
        {
            var tenantUsers = await GetTenantUsers(organizationId);
            var tenantUsersEmails = tenantUsers.Select(x => x.Email).ToList();
            var allUsersQuery = _userRepository.GetUsersWithPersonByOrganization(organizationId);
            var allUsers = await allUsersQuery.Select(u => _mapper.Map<PersonDTO>(u)).ToListAsync();
            return allUsers.Where(u => !tenantUsersEmails.Contains(u.Email)).ToList();
        }

        public async Task<List<string>> GetTenantRoles(int organizationId)
        {
            var mapping = await _organizationMappingRepository.GetTenaxMappingByOrganization(organizationId, true);
            if (mapping != null && mapping.CustomerTenantId != null)
            {
                return await _tenaxApi.GetTenantRoles();
            }
            else if (mapping != null && mapping.MSPId != null)
            {
                return await _tenaxApi.GetMSPRoles();
            }
            else
            {
                throw new BadRequestException(badRequestMessage);
            }
        }

        public async Task CreateUser(int organizationId, CreateUserRequest request)
        {
            var mapping = await _organizationMappingRepository.GetTenaxMappingByOrganization(organizationId, true);
            if (mapping != null && mapping.CustomerTenantId != null)
            {
                await _tenaxApi.CreateTenantUser(mapping.CustomerTenantId, request);
            }
            else if (mapping != null && mapping.MSPId != null)
            {
                await _tenaxApi.CreateMSPUser(mapping.MSPId, request);
            }
            else
            {
                throw new BadRequestException(badRequestMessage);
            }
        }

        public async Task ActivateUser(int organizationId, string userId)
        {
            var mapping = await _organizationMappingRepository.GetTenaxMappingByOrganization(organizationId, true);
            if (mapping != null && mapping.CustomerTenantId != null)
            {
                await _tenaxApi.ActivateTenantUser(mapping.CustomerTenantId, userId);
            }
            else if (mapping != null && mapping.MSPId != null)
            {
                await _tenaxApi.ActivateMSPUser(mapping.MSPId, userId);
            }
            else
            {
                throw new BadRequestException(badRequestMessage);
            }
        }

        public async Task DeactivateUser(int organizationId, string userId)
        {
            var mapping = await _organizationMappingRepository.GetTenaxMappingByOrganization(organizationId, true);
            if (mapping != null && mapping.CustomerTenantId != null)
            {
                await _tenaxApi.DeactivateTenantUser(mapping.CustomerTenantId, userId);
            }
            else if (mapping != null && mapping.MSPId != null)
            {
                await _tenaxApi.DeactivateMSPUser(mapping.MSPId, userId);
            }
            else
            {
                throw new BadRequestException(badRequestMessage);
            }
        }

        public async Task ReinviteUser(int organizationId, string userId)
        {
            var mapping = await _organizationMappingRepository.GetTenaxMappingByOrganization(organizationId, true);
            if (mapping != null && mapping.CustomerTenantId != null)
            {
                await _tenaxApi.ReinviteTenantUser(mapping.CustomerTenantId, userId);
            }
            else if (mapping != null && mapping.MSPId != null)
            {
                await _tenaxApi.ReinviteMSPUser(mapping.MSPId, userId);
            }
            else
            {
                throw new BadRequestException(badRequestMessage);
            }
        }

        public async Task UpdateUser(int organizationId, string userId, EditUserRequest request)
        {
            var mapping = await _organizationMappingRepository.GetTenaxMappingByOrganization(organizationId, true);
            if (mapping != null && mapping.CustomerTenantId != null)
            {
                await _tenaxApi.UpdateTenantUser(mapping.CustomerTenantId, userId, request);
            }
            else if (mapping != null && mapping.MSPId != null)
            {
                await _tenaxApi.UpdateMSPUser(mapping.MSPId, userId, request);
            }
            else
            {
                throw new BadRequestException("The Tenant is inexistent.");
            }
        }

        private async Task<List<TenaxTenantDTO>> GetTenants(bool includeUsers = false)
        {
            var result = await _tenaxApi.GetTenants(includeUsers);
            return _mapper.Map<List<TenaxTenantDTO>>(result);
        }

        private async Task<List<TenaxTenantDTO>> GetMSPs(bool includeUsers = false)
        {
            var result = await _tenaxApi.GetMSPs(includeUsers);
            return _mapper.Map<List<TenaxTenantDTO>>(result);
        }


        private async Task MapPartnerOrganization(string newTenantId, string organizationName, int organizationId, int currentUserId)
        {
            var newMapping = new CreateTenaxMappingRequest
            {
                PrimaryId = newTenantId,
                PrimaryName = organizationName
            };
            await _organizationMappingService.CreateTenaxMapping(organizationId, currentUserId, newMapping);
        }

        private async Task MapCustomerOrganization(string newTenantId, int partnerOrganizationId, int customerOrganizationId, int currentUserId)
        {
            var parentOrganizationMapping = await _organizationMappingService.GetTenaxMappingByOrganization(partnerOrganizationId);
            var newMapping = new CreateTenaxMappingRequest
            {
                PrimaryId = parentOrganizationMapping.PrimaryId,
                PrimaryName = parentOrganizationMapping.PrimaryName,
                SecondaryId = newTenantId,
                SecondaryName = parentOrganizationMapping.SecondaryName
            };
            await _organizationMappingService.CreateTenaxMapping(customerOrganizationId, currentUserId, newMapping);
        }

        private async Task<List<TenaxOrganizationModel>> GetSubOrganizationsDataQuery(int organizationId, string organizationName)
        {
            var query = _dbContext.Organization.AsNoTracking().IgnoreQueryFilters()
                .Include(query => query.OrganizationMappings)
                .Where(o => o.IsActive && o.ParentOrganizationId == organizationId);
            if (!string.IsNullOrEmpty(organizationName))
            {
                query = query.Where(o => o.Name.ToLower().Contains(organizationName.ToLower()));
            }

            var result = await query.OrderBy(o => o.Name).Select(o => new
            {
                o.Name,
                o.OrganizationId,
                o.IsPartner,
                o.AllowSubOrg,
                om = o.OrganizationMappings.SingleOrDefault(om => om is TenaxOrganizationMapping)
            }).ToListAsync();

            return result.Select(o => new TenaxOrganizationModel
            {
                OrganizationName = o.Name,
                OrganizationId = o.OrganizationId,
                IsPartner = o.IsPartner,
                AllowSubOrg = o.AllowSubOrg,
                MSPId = o.om != null ? (o.om as TenaxOrganizationMapping).MSPId : null,
                MSPName = o.om != null ? (o.om as TenaxOrganizationMapping).MSPName : null,
                CustomerTenantId = o.om != null ? (o.om as TenaxOrganizationMapping).CustomerTenantId : null,
                CustomerName = o.om != null ? (o.om as TenaxOrganizationMapping).CustomerName : null,
            }).ToList();
        }
    }
}