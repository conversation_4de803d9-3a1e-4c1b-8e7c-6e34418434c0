import { inject, Injectable } from '@angular/core';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { VIRTUAL_MACHINES_ENDPOINT_NAMES } from '../models/vm.constants';

@Injectable({
    providedIn: 'root'
})
export class VmManagementPermissionService {

    private readonly cloudInfraPermissionService = inject(CloudInfraPermissionService);

    public canViewVirtualMachineList(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.listVirtualMachines);
    }

    public canStopVirtualMachine(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.stopVirtualMachine);
    }

    public canDestroyVirtualMachine(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.destroyVirtualMachine);
    }

    public canRebootVirtualMachine(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.rebootVirtualMachine);
    }

    public canStartVirtualMachine(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.startVirtualMachine);
    }

    public canResetSSHKeyPairForVirtualMachine(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() ||
            (this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.resetSSHKeyForVirtualMachine) && this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.listSSHKeyPairs));
    }

    public canSnapshotVirtualMachine(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.snapshotVirtualMachine);
    }

    public canExpungeVirtualMachine(): boolean {
        return (this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.expungeVirtualMachine)) && this.cloudInfraPermissionService.isAdmin();
    }

    public canRecoverVirtualMachine(): boolean {
        return (this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.recoverVirtualMachine)) && this.cloudInfraPermissionService.isAdmin();
    }

    public canAttachIso(): boolean {
        return (this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.attachIso));
    }

    public canEjectIso(): boolean {
        return (this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.ejectIso));
    }

    public canMigrateVirtualMachineHost(): boolean {
        return (this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.migrateVirtualMachine));
    }

    public canSnapshotVolume(): boolean {
        return (this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.snapshotVolume));
    }

    public canReinstallVirtualMachine(): boolean {
        return (this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.reinstallVirtualMachine));
    }

    public canResetVirtualMachinePassword(): boolean {
        return (this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.resetVirtualMachinePassword));
    }

    public canCreateNetwork(): boolean {
        return (this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.createNetwork));
    }

    public canListAffinityGroups(): boolean {
        return (this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.listAffinityGroups));
    }

    public canUpdateVmAffinityGroups(): boolean {
        return (this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.updateVirtualMachineAffinityGroups));
    }

    public canCreateAffinityGroup(): boolean {
        return (this.cloudInfraPermissionService.hasAllPermissions() || (this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.listAffinityGroupTypes) &&
            this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.createAffinityGroup)));
    }

    public canListVmSnapshots(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.listVirtualMachineSnapshots);
    }

    public canCreateSnapshotFromVirtualMachineSnapshot(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.createSnapshotFromVirtualMachineSnapshot);
    }

    public canDeleteVirtualMachineSnapshot(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.deleteVirtualMachineSnapshot);
    }

    public canRevertToVirtualMachineSnapshot(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VIRTUAL_MACHINES_ENDPOINT_NAMES.revertToVirtualMachineSnapshot);
    }

}
