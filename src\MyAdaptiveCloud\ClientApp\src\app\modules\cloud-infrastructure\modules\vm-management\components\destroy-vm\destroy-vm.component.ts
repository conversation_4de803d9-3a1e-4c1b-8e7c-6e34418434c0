import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, OnInit, signal } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { VmDestroyForm } from '../../forms/vm-destroy.form';
import { VmManagementService } from '../../services/vm-management.service';
import { VolumeType } from '@app/modules/cloud-infrastructure/models/volumes/volume-type';

@Component({
    selector: 'app-destroy-vm',
    imports: [ReactiveFormsModule, BtnSubmitComponent],
    templateUrl: './destroy-vm.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class DestroyVmComponent implements OnInit {
    private readonly formBuilder = inject(FormBuilder);
    protected readonly activeModal = inject(NgbActiveModal);
    private readonly vmManagementService = inject(VmManagementService);
    protected readonly cloudInfraPermissionService = inject(CloudInfraPermissionService);
    private readonly changeDetectorRef = inject(ChangeDetectorRef);

    readonly inputData = signal<{
        virtualMachineId: string;
        domainId: string;
        account: string;
    }>(null);

    protected form: FormGroup<VmDestroyForm>;
    protected readonly volumes = signal<{ id: string, name: string }[]>([]);

    protected readonly isSubmitting = signal<boolean>(false);

    ngOnInit(): void {
        this.vmManagementService.getVolumesByVirtualMachine(this.inputData().virtualMachineId, this.inputData().domainId, this.inputData().account, VolumeType.DataDisk).subscribe(res => {
            this.volumes.set(res ? res.map(volume => ({ id: volume.id, name: volume.name })) : []);
            this.form = this.formBuilder.group<VmDestroyForm>({
                expunge: this.formBuilder.control<boolean>(false, Validators.required),
                volumes: this.formBuilder.array<boolean>(this.volumes().length ? this.volumes().map(() => false) : [])
            });
            this.changeDetectorRef.detectChanges();
        });
    }

    protected cancel() {
        this.activeModal.close();
    }

    protected destroyVirtualMachine() {
        this.isSubmitting.set(true);
        if (this.form.valid) {

            const { expunge, volumes } = this.form.value;
            const volumesToDelete = this.volumes().filter((_, i) => volumes[i] === true)
                .map(v => v.id);

            this.vmManagementService.destroyVirtualMachine(this.inputData().virtualMachineId, expunge, volumesToDelete)
                .subscribe(jobId => {
                    this.isSubmitting.set(false);
                    if (jobId) {
                        this.activeModal.close(jobId);
                    }
                });
        }
    }

}
