import { VmStateEnum } from '@app/shared/models/cloud-infra/vm-state.enum';
import { VolumeState } from './volume-state';
import { VolumeType } from './volume-type';

export interface Volume {
    account: string;
    attached?: string;
    created: string;
    destroyed: boolean;
    deviceid: number;
    diskioread?: number;
    diskiowrite?: number;
    diskkbsread?: number;
    diskkbswrite?: number;
    displayvolume: boolean;
    domain: string;
    domainid: string;
    hasannotations: boolean;
    hypervisor: string;
    id: string;
    isextractable: boolean;
    name: string;
    path?: string;
    physicalsize?: number;
    provisioningtype: string;
    quiescevm: boolean;
    serviceofferingdisplaytext: string;
    serviceofferingid: string;
    serviceofferingname: string;
    size: number;
    sizegb: string;
    state: VolumeState;
    storage?: string;
    storageid?: string;
    storagetype: string;
    supportsstoragesnapshot: boolean;
    tags: []
    templatedisplaytext: string;
    templateid: string;
    templatename: string;
    type: VolumeType;
    utilization?: string;
    virtualmachineid: string;
    virtualsize: number;
    vmdisplayname?: string;
    vmname?: string;
    vmstate?: VmStateEnum;
    vmtype: string;
    zoneid: string;
    zonename: string;
}
