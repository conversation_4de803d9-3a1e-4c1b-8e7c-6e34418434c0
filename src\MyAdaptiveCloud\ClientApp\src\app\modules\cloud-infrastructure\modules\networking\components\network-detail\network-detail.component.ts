import { ChangeDetectionStrategy, Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { CreateNetworkService } from '@app/modules/cloud-infrastructure/services/create-network.service';
import { BtnSubmitComponent } from '@app/shared/components/btn-submit/btn-submit.component';
import { NgSelectComponent } from '@ng-select/ng-select';
import { NetworkDetailForm } from '../../models/network-detail-form';
import { NetworkingDetailService } from '../../services/networking-detail.service';
import { NetworkingService } from './../../services/networking.service';

@Component({
    selector: 'app-network-detail',
    imports: [ReactiveFormsModule, BtnSubmitComponent, NgSelectComponent],
    templateUrl: './network-detail.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})

export class NetworkDetailComponent {

    protected readonly networkingDetailService = inject(NetworkingDetailService);
    private readonly networkingService = inject(NetworkingService);
    private readonly createNetworkService = inject(CreateNetworkService);
    private readonly destroyRef = inject(DestroyRef);
    private readonly formBuilder = inject(FormBuilder);
    protected readonly networkOfferings = toSignal(this.createNetworkService.getNetworkOfferings(this.networkingDetailService.selectedNetwork()));

    protected form: FormGroup<NetworkDetailForm> = this.formBuilder.group<NetworkDetailForm>({
        name: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().name, disabled: true }, [Validators.required]),
        description: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().displaytext, disabled: true }, [Validators.required]),
        cidr: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().cidr, disabled: true }, [Validators.required]),
        networkDomain: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().networkdomain, disabled: true }, [Validators.required]),
        zoneName: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().zonename, disabled: true }),
        domain: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().domain, disabled: true }),
        account: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().account, disabled: true }),
        type: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().type, disabled: true }),
        id: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().id, disabled: true }),
        networkOfferingId: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().networkofferingid, disabled: true }, [Validators.required]),
        restartrequired: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().restartrequired ? 'Yes' : 'No', disabled: true }),
        redundantrouter: this.formBuilder.control({ value: this.networkingDetailService.selectedNetwork().redundantrouter ? 'Yes' : 'No', disabled: true })
    });

    constructor() {
        toObservable(this.networkingDetailService.isEditMode)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(editMode => {
                if (editMode) {
                    this.form.controls.name.enable();
                    this.form.controls.description.enable();
                    this.form.controls.networkOfferingId.enable();
                    this.form.controls.cidr.enable();
                    this.form.controls.networkDomain.enable();
                } else {
                    this.form.controls.name.disable();
                    this.form.controls.description.disable();
                    this.form.controls.networkOfferingId.disable();
                    this.form.controls.cidr.disable();
                    this.form.controls.networkDomain.disable();
                }
            });
    }

    protected submitForm() {
        if (this.form.valid) {
            this.networkingService.editNetwork(
                this.networkingDetailService.selectedNetwork().id,
                this.form.value.name,
                this.form.value.description,
                this.form.value.cidr,
                this.form.value.networkDomain,
                this.form.value.networkOfferingId
            ).subscribe(() => {
                this.networkingDetailService.updateSelectedNetwork(this.form.value.name, this.form.value.description);
                this.networkingDetailService.isEditMode.set(false);
            });
        }
    }
}

