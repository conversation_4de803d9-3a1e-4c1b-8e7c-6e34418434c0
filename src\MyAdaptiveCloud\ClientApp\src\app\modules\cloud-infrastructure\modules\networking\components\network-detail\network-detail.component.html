@if(form) {
    <form [formGroup]="form">
        <div class="general-details-middle card p-4">
            <div class="row gy-2">
                <div class="col-md-6">
                    <div class="mb-2">
                        <label class="text-secondary fw-bold">Name</label>
                        <input class="form-control" formControlName="name"
                            [class]="{ 'is-invalid': form.controls.name.invalid && form.controls.name.dirty }" />
                    </div>
                    <div class="mb-2">
                        <label class="text-secondary fw-bold">CIDR</label>
                        <input class="form-control" formControlName="cidr"
                            [class]="{ 'is-invalid': form.controls.cidr.invalid && form.controls.cidr.dirty }" />
                    </div>
                    <div class="mb-2">
                        <label class="text-secondary fw-bold">Network Domain</label>
                        <input class="form-control" formControlName="networkDomain"
                            [class]="{ 'is-invalid': form.controls.networkDomain.invalid && form.controls.networkDomain.dirty }" />
                    </div>
                    <div class="mb-2">
                        <label class="text-secondary fw-bold">Zone</label>
                        <input class="form-control" formControlName="zoneName" />
                    </div>
                    <div class="mb-2">
                        <label class="text-secondary fw-bold">Domain</label>
                        <input class="form-control" formControlName="domain" />
                    </div>
                    <div class="mb-2">
                        <label class="text-secondary fw-bold">Account</label>
                        <input class="form-control" formControlName="account" />
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-2">
                        <label class="text-secondary fw-bold">Description</label>
                        <input class="form-control" formControlName="description" />
                    </div>
                    <div class="mb-2">
                        <label class="text-secondary fw-bold">Type</label>
                        <input class="form-control" formControlName="type">
                    </div>
                    <div class="mb-2">
                        <label class="text-secondary fw-bold">ID</label>
                        <input class="form-control" formControlName="id">
                    </div>
                    <div class="mb-2">
                        <label class="text-secondary fw-bold">Network Offering</label>
                        <ng-select [items]="networkOfferings()" bindLabel="name" bindValue="id" formControlName="networkOfferingId"
                            [class]="{ 'is-invalid': form.controls.networkOfferingId.invalid && form.controls.networkOfferingId.dirty }" />
                    </div>
                    <div class="mb-2">
                        <label class="text-secondary fw-bold">Restart Required</label>
                        <input class="form-control" formControlName="restartrequired">
                    </div>
                    <div class="mb-2">
                        <label class="text-secondary fw-bold">Redundant VPC</label>
                        <input class="form-control" formControlName="redundantrouter">
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer d-flex justify-content-end gap-2">
            <button type="button" class="btn btn-outline-secondary"
                (click)="networkingDetailService.isEditMode.set(false)">Cancel</button>
            <app-btn-submit [btnClasses]="'btn-primary'" [disabled]="!form?.valid" (submitClickEvent)="submitForm()">Save
            </app-btn-submit>
        </div>
    </form>
}
