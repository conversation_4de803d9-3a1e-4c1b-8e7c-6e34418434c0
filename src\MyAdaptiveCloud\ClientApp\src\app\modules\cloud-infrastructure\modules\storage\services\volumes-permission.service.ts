import { inject, Injectable } from '@angular/core';
import { VOLUMES_ENDPOINT_NAMES } from '@app/modules/cloud-infrastructure/models/volumes/volume.constants';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { VolumesPermission } from '../../../models/volumes/volumes-permission';

@Injectable({
    providedIn: 'root'
})
export class VolumesPermissionService {

    private readonly cloudInfraPermissionService = inject(CloudInfraPermissionService);

    /**
     * Retrieves the permissions related to volumes for the current user.
     * @returns An object containing boolean flags for each volume-related permission.
     */
    public getVolumesPermissionsForCurrentUser(): VolumesPermission {
        const hasAllPermissions = this.cloudInfraPermissionService.hasAllPermissions();

        return {
            listSnapshots: hasAllPermissions || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.listSnapshots),
            createVolume: hasAllPermissions || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.createVolume),
            uploadVolume: hasAllPermissions || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.uploadVolume),
            createSnapshot: hasAllPermissions || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.createSnapshot),
            createSnapshotPolicy: hasAllPermissions || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.createSnapshotPolicy),
            attachVolume: hasAllPermissions || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.attachVolume),
            createTemplate: hasAllPermissions || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.createTemplate),
            migrateVolume: hasAllPermissions || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.migrateVolume),
            destroyVolume: hasAllPermissions || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.destroyVolume),
            resizeVolume: hasAllPermissions || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.resizeVolume),
            extractVolume: hasAllPermissions || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.extractVolume),
            detachVolume: hasAllPermissions || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.detachVolume)
        };
    }

    canListVolumes(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.listVolumes);
    }

    canListSnapshots(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.listSnapshots);
    }

    canListVMSnapshots(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.listVMSnapshots);
    }
}
