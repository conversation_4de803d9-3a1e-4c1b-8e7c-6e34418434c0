import { Async<PERSON>ipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, TemplateRef, viewChild } from '@angular/core';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { TableActionComponent } from '@app/shared/components/table-action/table-action.component';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { ModalService } from '@app/shared/services/modal.service';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { filter, map, Observable, take } from 'rxjs';
import { AffinityGroupViewModel } from '../../models/affinity-group.view-model';
import { VmAffinityGroupsService } from '../../services/vm-affinity-groups.service';
import { VmDetailsStateService } from '../../services/vm-details.state.service';
import { VmManagementPermissionService } from '../../services/vm-management-permission.service';
import { AddToAffinityGroupComponent } from '../add-to-affinity-group/add-to-affinity-group.component';

@Component({
    selector: 'app-vm-details-affinity-groups',
    imports: [NgxDatatableModule, AutoSearchBoxComponent, TableActionComponent, AsyncPipe],
    templateUrl: './vm-details-affinity-groups.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class VmDetailsAffinityGroupsComponent extends BaseListClientComponent<AffinityGroupViewModel> implements OnInit {

    protected readonly vmDetailsStateService = inject(VmDetailsStateService);
    private readonly vmAffinityGroupsService = inject(VmAffinityGroupsService);
    protected readonly vmManagementPermissionService = inject(VmManagementPermissionService);
    private readonly modalService = inject(ModalService);

    private readonly headerTemplate = viewChild<TemplateRef<never>>('headerTemplate');
    private readonly actionsTemplate = viewChild<TemplateRef<never>>('actionsTemplate');

    ngOnInit(): void {
        const columns: TableColumn[] = [
            {
                name: 'Name',
                prop: 'name',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 200
            },
            {
                name: 'Description',
                prop: 'description',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                resizeable: true,
                canAutoResize: true,
                width: 300
            },
            {
                name: 'Type',
                prop: 'type',
                headerTemplate: this.headerTemplate(),
                sortable: false,
                resizeable: true,
                canAutoResize: true,
                width: 200
            }
        ];

        if (this.vmManagementPermissionService.canUpdateVmAffinityGroups()) {
            columns.push({
                name: 'Actions',
                prop: '',
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                resizeable: false,
                canAutoResize: false,
                width: 100
            });
        }

        super.initialize(() => this.getAffinityGroups$(), columns);
    }

    private getAffinityGroups$(): Observable<ApiDataResult<AffinityGroupViewModel[]>> {
        // The affinity groups are loaded through the VmDetailsService because the ones already included in the VM details
        // don't include the type property
        return this.vmAffinityGroupsService.getAffinityGroupsByVirtualMachine(this.vmDetailsStateService.selectedVM().id)
            .pipe(map(res => ({
                data: res.map(affinityGroup => ({
                    id: affinityGroup.id,
                    name: affinityGroup.name?.trim() ?? '',
                    description: affinityGroup.description?.trim() ?? '',
                    type: affinityGroup.type?.trim() ?? ''
                })),
                total: res.length,
                message: ''
            })));
    }

    protected removeAffinityGroup(affinityGroupId: string): void {
        this.modalService.openDeleteConfirmationDialog('Remove Virtual Machine from Affinity Group', 'Are you sure you want to remove the Virtual Machine from this Affinity Group?')
            .closed
            .pipe(
                filter(res => !!res),
                take(1)
            ).subscribe(() => {
                const updatedAffinityGroups = this.vmDetailsStateService.selectedVM().affinitygroup?.filter(ag => ag.id !== affinityGroupId).map(affinityGroup => affinityGroup.id) ?? [];
                this.vmAffinityGroupsService.updateAffinityGroupsForVirtualMachine(this.vmDetailsStateService.selectedVM().id, updatedAffinityGroups)
                    .subscribe();
            });
    }

    protected addToAffinityGroup(): void {
        const modalRef = this.modalService.openModalComponent(AddToAffinityGroupComponent);
        (modalRef.componentInstance as AddToAffinityGroupComponent).domainId.set(this.vmDetailsStateService.selectedVM().domainid);
        (modalRef.componentInstance as AddToAffinityGroupComponent).account.set(this.vmDetailsStateService.selectedVM().account);
        (modalRef.componentInstance as AddToAffinityGroupComponent).virtualMachineId.set(this.vmDetailsStateService.selectedVM().id);
        modalRef.closed
            .pipe(
                filter(res => !!res),
                take(1)
            )
            .subscribe();
    }

}
