import { TestBed } from '@angular/core/testing';
import { CloudInfraAccountViewModel } from '@app/modules/cloud-infrastructure/models/cloud-infra-account.view-model';
import { CloudInfraDomainViewModel } from '@app/modules/cloud-infrastructure/models/cloud-infra-domain.view-model';
import { CloudInfraAccountService } from '@app/modules/cloud-infrastructure/services/cloud-infra-account.service';
import { CloudInfraDomainService } from '@app/modules/cloud-infrastructure/services/cloud-infra-domain.service';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { CloudInfraUserContext } from '@app/shared/models/cloud-infra-user-context';
import { UserContext } from '@app/shared/models/user-context.model';
import { CloudInfrastructureSessionService } from '@app/shared/services/cloud-infrastructure-session.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { of } from 'rxjs';
import { CloudInfraZoneService } from '../services/cloud-infra-zone.service';

describe('ZoneDomainAccountStore', () => {
    let store;
    let mockCloudInfraAccountService: jasmine.SpyObj<CloudInfraAccountService>;
    let mockCloudInfraDomainService: jasmine.SpyObj<CloudInfraDomainService>;
    let mockCloudInfrastructureSessionService: jasmine.SpyObj<CloudInfrastructureSessionService>;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockCloudInfraZoneService: jasmine.SpyObj<CloudInfraZoneService>;

    const account1: CloudInfraAccountViewModel = {
        id: '1',
        name: 'Account 1',
        domainId: '351434a0-c7e0-11eb-bbc8-005056b1c79a',
    };

    const domain2: CloudInfraDomainViewModel = {
        accounts: [],
        hasChild: false,
        id: '351434a0-c7e0-11eb-bbc8-005056b1c791',
        isExpanded: true,
        level: 1,
        name: 'Gorilla',
        subDomains: []
    };

    const domain1: CloudInfraDomainViewModel = {
        accounts: [account1],
        hasChild: true,
        id: '351434a0-c7e0-11eb-bbc8-005056b1c79a',
        isExpanded: true,
        level: 0,
        name: 'ROOT',
        subDomains: [domain2]
    };

    const mockZones = [{ name: 'zone1', id: '1' }, { name: 'zone2', id: '2' }];

    beforeEach(() => {

        TestBed.configureTestingModule({
            providers: [
                ZoneDomainAccountStore,
                provideMock(CloudInfrastructureSessionService),
                provideMock(CloudInfraAccountService),
                provideMock(CloudInfraDomainService),
                provideMock(UserContextService),
                provideMock(CloudInfraZoneService)
            ]
        });

        mockCloudInfraAccountService = TestBed.inject(CloudInfraAccountService) as jasmine.SpyObj<CloudInfraAccountService>;
        mockCloudInfraDomainService = TestBed.inject(CloudInfraDomainService) as jasmine.SpyObj<CloudInfraDomainService>;

        mockCloudInfrastructureSessionService = TestBed.inject(CloudInfrastructureSessionService) as jasmine.SpyObj<CloudInfrastructureSessionService>;
        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;

        mockCloudInfraAccountService.getAccountById.and.returnValue(of([account1]));
        mockCloudInfraDomainService.getDomainList.and.returnValue(of([domain1]));
        mockCloudInfraAccountService.getAccountsByDomainId.and.returnValue(of([account1]));
        mockCloudInfraDomainService.getDomainChildrenList.and.returnValue(of([domain2]));

        mockCloudInfraZoneService = TestBed.inject(CloudInfraZoneService) as jasmine.SpyObj<CloudInfraZoneService>;
        mockCloudInfraZoneService.getZones.and.returnValue(of(mockZones));
    });

    describe('Initialization', () => {

        it('should initialize store with account when the organization does not have a domainId mapped', () => {
            const cloudInfraContext: CloudInfraUserContext = {
                apiKey: 'Yre9R_RkK_1Vls2VUQfppp6NW140BIpaKtJH1K7hkMlTq7iU7MVNamrv0G9l8K2K6H1a2al4VVnay_nJKnH7uw',
                secretKey: 'NYjBP6PrsDX6tilLnveSTYllM-WEGTQy66Kz91klgkftDaj3-bTC6XAwz9fkCJcFlgBwM5g5fqE00f7HcaDog',
                permissions: [
                    '*'
                ],
                roleName: 'Root Admin',
                roleType: 'Admin',
                accountName: 'Root',
                accountId: '59029ecf-c7e0-11eb-bbc8-005056b1c79a',
                domainId: '351434a0-c7e0-11eb-bbc8-005056b1c79a',
                apiUrl: 'https://labmy.ippathways.us/client',
                apiVersion: '4.15',
                cpuCustomOfferingMaxValue: 4,
                memoryCustomOfferingMaxValue: 8192,
                diskSizeCustomOfferingMaxValue: 1024,
                diskSizeCustomOfferingMinValue: 1,
                hasMappedDomain: false
            };
            mockCloudInfrastructureSessionService.login.and.returnValue(of(cloudInfraContext));
            mockUserContextService.currentUser = { organizationId: 1, cloudInfraUserContext: cloudInfraContext } as UserContext;

            store = TestBed.inject(ZoneDomainAccountStore);

            expect(mockCloudInfraDomainService.getDomainList).not.toHaveBeenCalled();
            expect(mockCloudInfraAccountService.getAccountById).toHaveBeenCalledOnceWith(cloudInfraContext.accountId);
            expect(store.zones()).toEqual(mockZones);
            expect(store.getAccount()).toBe(account1.name);
            expect(store.selectedAccount()).toEqual(account1);
            expect(store.domains()).toEqual([]);
            expect(store.accounts()).toEqual([account1]);
            expect(store.mainDomain()).toBeNull();
        });

        it('should initialize store with domain when the organization has a domainId mapped', () => {
            const cloudInfraContext: CloudInfraUserContext = {
                apiKey: 'Yre9R_RkK_1Vls2VUQfppp6NW140BIpaKtJH1K7hkMlTq7iU7MVNamrv0G9l8K2K6H1a2al4VVnay_nJKnH7uw',
                secretKey: 'NYjBP6PrsDX6tilLnveSTYllM-WEGTQy66Kz91klgkftDaj3-bTC6XAwz9fkCJcFlgBwM5g5fqE00f7HcaDog',
                permissions: [
                    '*'
                ],
                roleName: 'Root Admin',
                roleType: 'Admin',
                accountName: 'Root',
                accountId: '59029ecf-c7e0-11eb-bbc8-005056b1c79a',
                domainId: '351434a0-c7e0-11eb-bbc8-005056b1c79a',
                apiUrl: 'https://labmy.ippathways.us/client',
                apiVersion: '4.15',
                cpuCustomOfferingMaxValue: 4,
                memoryCustomOfferingMaxValue: 8192,
                diskSizeCustomOfferingMaxValue: 1024,
                diskSizeCustomOfferingMinValue: 1,
                hasMappedDomain: true
            };
            mockCloudInfrastructureSessionService.login.and.returnValue(of(cloudInfraContext));
            mockUserContextService.currentUser = { organizationId: 1, isPartner: true, cloudInfraUserContext: cloudInfraContext } as UserContext;

            store = TestBed.inject(ZoneDomainAccountStore);

            expect(mockCloudInfraAccountService.getAccountById).not.toHaveBeenCalled();
            expect(mockCloudInfraDomainService.getDomainList).toHaveBeenCalledOnceWith(cloudInfraContext.domainId);
            expect(store.zones()).toEqual(mockZones);
            expect(store.getAccount()).toBeNull();
            expect(store.selectedAccount()).toBeNull();
            expect(store.domains()).toEqual([domain1]);
            expect(store.accounts()).toEqual([]);
            expect(store.mainDomain()).toEqual(domain1);
        });

    });

});
