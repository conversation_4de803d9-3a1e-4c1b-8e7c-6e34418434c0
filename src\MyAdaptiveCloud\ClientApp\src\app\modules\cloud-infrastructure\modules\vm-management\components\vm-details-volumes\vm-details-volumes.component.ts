import { DecimalPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, OnInit, signal, TemplateRef, viewChild } from '@angular/core';
import { AutoSearchBoxComponent } from '@app/shared/components/datatable/auto-search-box/auto-search-box.component';
import { ApiDataResult } from '@app/shared/models/api-service/api.data.result';
import { BaseListClientComponent } from '@app/shared/models/datatable/base-list-client.component.model';
import { NgbDropdown, NgbDropdownMenu, NgbDropdownToggle } from '@ng-bootstrap/ng-bootstrap';
import { NgxDatatableModule, TableColumn } from '@swimlane/ngx-datatable';
import { map, Observable } from 'rxjs';
import { VolumeViewModel } from '../../models/volume.view-model';
import { VmDetailsStateService } from '../../services/vm-details.state.service';
import { VmVolumesService } from '../../services/vm-volumes.service';
import { byteToGB } from '../../utils/functions';
import { VolumeState } from '@app/modules/cloud-infrastructure/models/volumes/volume-state';

@Component({
    selector: 'app-vm-details-volumes',
    imports: [NgxDatatableModule, AutoSearchBoxComponent, NgbDropdown, NgbDropdownMenu, NgbDropdownToggle, DecimalPipe],
    templateUrl: './vm-details-volumes.component.html',
    styleUrl: './vm-details-volumes.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class VmDetailsVolumesComponent extends BaseListClientComponent<VolumeViewModel> implements OnInit {

    private readonly vmDetailsStateService = inject(VmDetailsStateService);
    private readonly vmVolumesService = inject(VmVolumesService);

    protected readonly viewMode = signal<'volumes' | 'metrics'>('volumes');
    protected readonly volumeState = VolumeState;

    private readonly headerTemplate = viewChild<TemplateRef<never>>('headerTemplate');
    private readonly stateTemplate = viewChild<TemplateRef<never>>('stateTemplate');
    private readonly actionsTemplate = viewChild<TemplateRef<never>>('actionsTemplate');
    private readonly numberTemplate = viewChild<TemplateRef<never>>('numberTemplate');
    private readonly gbTemplate = viewChild<TemplateRef<never>>('gbTemplate');

    private metricColumns: TableColumn<VolumeViewModel>[];
    private volumeColumns: TableColumn<VolumeViewModel>[];

    ngOnInit(): void {

        const sharedColumns = [
            {
                name: 'State',
                prop: 'state',
                cellTemplate: this.stateTemplate(),
                headerTemplate: this.headerTemplate(),
                sortable: true,
                canAutoResize: true
            },
            {
                name: 'Volume Name',
                prop: 'name',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                canAutoResize: true
            }
        ];

        const actionColumn = [
            {
                name: 'Actions',
                width: 100,
                cellTemplate: this.actionsTemplate(),
                sortable: false,
                canAutoResize: false
            }
        ];

        this.volumeColumns = [
            ...sharedColumns,
            {
                name: 'Size',
                prop: 'size',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.gbTemplate(),
                sortable: true,
                canAutoResize: true
            },
            {
                name: 'Physical Size',
                prop: 'physicalSize',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.gbTemplate(),
                sortable: true,
                canAutoResize: true
            },
            {
                name: 'Utilization',
                prop: 'utilization',
                headerTemplate: this.headerTemplate(),
                sortable: true,
                canAutoResize: true
            },
            ...actionColumn
        ];

        this.metricColumns = [
            ...sharedColumns,
            {
                name: 'Disk Read (KiB)',
                prop: 'diskRead',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.numberTemplate(),
                sortable: true,
                canAutoResize: true
            },

            {
                name: 'Disk Write (KiB)',
                prop: 'diskWrite',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.numberTemplate(),
                sortable: true,
                canAutoResize: true
            },
            {
                name: 'Disk IOPS',
                prop: 'diskIOPS',
                headerTemplate: this.headerTemplate(),
                cellTemplate: this.numberTemplate(),
                sortable: true,
                canAutoResize: true
            },
            ...actionColumn
        ];

        super.initialize(() => this.getVolumeMetricsData$(), this.volumeColumns);

        this.table().sorts = [{ prop: 'name', dir: 'asc' }];
    }

    private getVolumeMetricsData$(): Observable<ApiDataResult<VolumeViewModel[]>> {
        return this.vmVolumesService.getVolumeMetricsByVirtualMachine(this.vmDetailsStateService.selectedVM().id).pipe(map(volumes => ({
            data: volumes.map(volume => {
                const volumeViewModel: VolumeViewModel = {
                    diskIOPS: (volume.diskioread ?? 0) + (volume.diskiowrite ?? 0),
                    diskRead: volume.diskioread ?? null,
                    diskWrite: volume.diskiowrite ?? null,
                    id: volume.id,
                    name: volume.name,
                    physicalSize: volume.physicalsize ? parseFloat(byteToGB(volume.physicalsize).toFixed(2)) : null,
                    state: volume.state,
                    size: parseFloat(byteToGB(volume.size).toFixed(2)),
                    utilization: volume.utilization ?? '-',
                };
                return volumeViewModel;
            }),
            message: ''
        })));
    }

    protected toggleViewMode(mode: 'volumes' | 'metrics'): void {
        this.viewMode.set(mode);
        const columns = mode === 'metrics' ? this.metricColumns : this.volumeColumns;
        this.table().columns = [...columns];
        this.table().recalculate();
    }

    protected onAddVolume(): void {
        // To be Implemented in a future story
    }
}
