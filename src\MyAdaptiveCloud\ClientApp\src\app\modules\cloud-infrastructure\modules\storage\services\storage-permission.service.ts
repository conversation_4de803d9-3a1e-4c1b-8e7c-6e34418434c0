import { inject, Injectable } from '@angular/core';
import { VOLUMES_ENDPOINT_NAMES } from '@app/modules/cloud-infrastructure/models/volumes/volume.constants';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { StoragePermission } from '../models/storage-permission';

@Injectable({
    providedIn: 'root'
})
export class StoragePermissionService {

    private readonly cloudInfraPermissionService = inject(CloudInfraPermissionService);

    /**
     * Retrieves the permissions related to volumes for the current user.
     * @returns An object containing boolean flags for each volume-related permission.
     */
    public getStoragePermissionsForCurrentUser(): StoragePermission {
        return {
            listVolumes: this.canListVolumes(),
            listSnapshots: this.canListSnapshots(),
            listVMSnapshots: this.canListVMSnapshots()
        };
    }

    canListVolumes(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.listVolumes);
    }

    canListSnapshots(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.listSnapshots);
    }

    canListVMSnapshots(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(VOLUMES_ENDPOINT_NAMES.listVMSnapshots);
    }
}
