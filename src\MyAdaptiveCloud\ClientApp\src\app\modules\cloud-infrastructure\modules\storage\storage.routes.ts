import { Routes } from '@angular/router';
import { cloudInfrastructureCredentialsGuard } from '../../guards/cloud-infrastructure-credentials.guard';
import { STORAGE_ROUTE_SEGMENTS } from './models/route.segments';
import { listVolumesGuard } from './guards/list-volumes.guard';
import { listSnapshotsGuard } from './guards/list-snapshots.guard';
import { listVMSnapshotsGuard } from './guards/list-vm-snapshots.guard';

export const storageRoutes: Routes = [
    {
        path: '',
        loadComponent: () => import('./components/storage-management/storage-management.component').then(m => m.StorageManagementComponent),
        canActivate: [cloudInfrastructureCredentialsGuard],
        canActivateChild: [cloudInfrastructureCredentialsGuard],
        children: [
            {
                path: '',
                redirectTo: STORAGE_ROUTE_SEGMENTS.VOLUMES,
                pathMatch: 'full'
            },
            {
                path: STORAGE_ROUTE_SEGMENTS.VOLUMES,
                canActivate: [listVolumesGuard],
                loadComponent: () => import('./components/volumes/volumes.component').then(m => m.VolumesComponent),
            },
            {
                path: STORAGE_ROUTE_SEGMENTS.SNAPSHOTS,
                canActivate: [listSnapshotsGuard],
                loadComponent: () => import('./components/snapshots/snapshots.component').then(m => m.SnapshotsComponent),
            },
            {
                path: STORAGE_ROUTE_SEGMENTS.VM_SNAPSHOTS,
                canActivate: [listVMSnapshotsGuard],
                loadComponent: () => import('./components/vm-snapshots/vm-snapshots.component').then(m => m.VMSnapshotsComponent),
            }
        ]
    }
];
