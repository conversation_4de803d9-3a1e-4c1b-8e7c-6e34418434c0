<div class="content-sub-heading d-flex justify-content-between">
    <div class="search-bar">
        <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" dataItemName='searchTerm' />
    </div>
    @let permissions = volumesPermissionService.getVolumesPermissionsForCurrentUser();

    <div class="action-buttons">
        @if (permissions.uploadVolume) {
            <div ngbDropdown class="d-inline-block">
                <button ngbDropdownToggle class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown"
                    aria-expanded="false">Upload</button>
                <ul ngbDropdownMenu>
                    <li><a ngbDropdownItem class="dropdown-item clickable">Upload</a></li>
                    <li><a ngbDropdownItem class="dropdown-item clickable">Upload from URL</a></li>
                </ul>
            </div>
        }
        <div class="btn-group" role="group">
            <button type="button" class="btn" [class.btn-primary]="viewMode() === 'volumes'"
                [class.btn-outline-primary]="viewMode() !== 'volumes'" (click)="toggleViewMode('volumes')"
                data-testid="volumes-toggle">
                Volumes
            </button>
            <button type="button" class="ms-0 btn" [class.btn-primary]="viewMode() === 'metrics'"
                [class.btn-outline-primary]="viewMode() !== 'metrics'" (click)="toggleViewMode('metrics')"
                data-testid="metrics-toggle">
                Metrics
            </button>
        </div>
        @if (permissions.createVolume){
            <button class="btn btn-primary">Create Volume</button>
        }
    </div>

</div>

<div class="card card-default">
    <div class="card-body">
        <ngx-datatable #table class='table bootstrap no-detail-row' (sort)="onSorting($event)"
            (page)="onPageChanged($event)" />
    </div>
</div>

<ng-template #stateCell let-row="row">
    <div>
        @if (toItem(row); as vol) {
            @let iconColor = {
            'text-warning': vol.state === volumeState.UploadNotStarted,
            'text-danger': vol.state === volumeState.UploadError,
            'text-secondary': vol.state === volumeState.Uploaded,
            'text-success': vol.state === volumeState.Ready,
            'text-primary': vol.state === volumeState.Allocated
            };
            <i class='fa-solid fa-circle me-1 status-icon' [class]="iconColor" data-testid="status-icon"></i>
            {{ vol.state }}
        }
    </div>
</ng-template>

<ng-template #numberCell let-value="value">
    {{ value ? (value | number) : '-' }}
</ng-template>

<ng-template #gbCell let-value="value">
    {{ value ? (value | fileSizeConverter:'B':'GB':true:false:2) : '-' }}
</ng-template>

<ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
    <span (click)="sort()" class="clickable">
        {{ column.name }}
        <span
            [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'"></span>
    </span>
</ng-template>

<ng-template #actionsTemplate let-row="row">
    @let isAdmin = cloudInfraPermissionService.isAdmin();
    @if (toItem(row); as vol) {
        @let isRootVolume = vol.type === volumeType.Root;
        @let isDataDisk = vol.type === volumeType.DataDisk;
        @let isDiskAttached = vol.isAttached;
        <span ngbDropdown class="dropdown" container="body">
            <button class="btn btn-link kebab-menu" ngbDropdownToggle>
                <i class="fa-solid fa-ellipsis-vertical"></i>
            </button>
            <div ngbDropdownMenu class="custom-dropdown-menu">
                @if (isRootVolume || isDataDisk) {
                    @if (permissions.listSnapshots){
                        <button class="dropdown-item">View Snapshots</button>
                    }
                    @if (permissions.createSnapshot){
                        <button class="dropdown-item">Create Snapshot</button>
                    }
                    @if (permissions.createSnapshotPolicy){
                        <button class="dropdown-item">Set Up Recurring Snapshot</button>
                    }
                    @if (isDataDisk && !isDiskAttached && permissions.attachVolume){
                        <button class="dropdown-item">Attach Disk</button>
                    }
                    @if (isDataDisk && isDiskAttached && permissions.detachVolume){
                        <button class="dropdown-item">Detach Disk</button>
                    }
                    @if (permissions.createTemplate){
                        <button class="dropdown-item">Create Template</button>
                    }
                    @if (isAdmin && (isRootVolume || !isDiskAttached) && permissions.migrateVolume){
                        <button class="dropdown-item">Migrate Volume</button>
                    }
                    @if (isAdmin && isDataDisk && !isDiskAttached){
                        <button class="dropdown-item">Assign Volume to Another Account</button>
                    }
                    @if (isDataDisk && !isDiskAttached && permissions.destroyVolume){
                        <button class="dropdown-item">Destroy Volume</button>
                    }
                    @if (permissions.resizeVolume){
                        <button class="dropdown-item">Resize Volume</button>
                    }
                    @if (permissions.extractVolume){
                        <button class="dropdown-item">Download Volume</button>
                    }
                }
            </div>
        </span>
    }
</ng-template>
