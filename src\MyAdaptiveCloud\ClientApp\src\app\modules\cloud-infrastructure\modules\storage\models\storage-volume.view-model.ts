import { VolumeState } from '@app/modules/cloud-infrastructure/models/volumes/volume-state';
import { VolumeType } from '@app/modules/cloud-infrastructure/models/volumes/volume-type';

export interface StorageVolumeViewModel {
    diskRead: number | null;
    diskWrite: number | null;
    diskIOPS: number;
    id: string;
    name: string;
    physicalSize: number | null;
    size: number | null;
    state: VolumeState;
    utilization: string;
    vmName: string;
    account: string;
    domain: string;
    domainId: string;
    zoneName: string;
    isAttached: boolean;
    type: VolumeType;
}
