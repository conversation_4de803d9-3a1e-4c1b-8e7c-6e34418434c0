import { DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { Volume } from '@app/modules/cloud-infrastructure/models/volumes/volume';
import { VolumeState } from '@app/modules/cloud-infrastructure/models/volumes/volume-state';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { getMockZoneDomainAccountStore, MockZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/mock-zone-domain-account.store';
import { ZoneDomainAccountStore } from '@app/modules/cloud-infrastructure/store/zone-domain-account-store';
import { CloudInfraUserContext } from '@app/shared/models/cloud-infra-user-context';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { DatatableComponent } from '@swimlane/ngx-datatable';
import { of } from 'rxjs';
import { VolumesPermissionService } from '../../services/volumes-permission.service';
import { VolumesService } from '../../services/volumes.service';
import { VolumesComponent } from './volumes.component';

describe('VolumesComponent', () => {
    let fixture: ComponentFixture<VolumesComponent>;
    let volumesServiceMock: jasmine.SpyObj<VolumesService>;
    let userContextServiceMock: jasmine.SpyObj<UserContextService>;
    let volumesPermissionServiceMock: jasmine.SpyObj<VolumesPermissionService>;
    let cloudInfraPermissionServiceMock: jasmine.SpyObj<CloudInfraPermissionService>;

    let volumes: Volume[];
    let zoneDomainAccountStoreMock: MockZoneDomainAccountStore;

    let dataTableDebugElement: DebugElement;
    let dataTable: HTMLElement;

    beforeEach(() => {
        zoneDomainAccountStoreMock = getMockZoneDomainAccountStore();

        volumes = [
            {
                id: 'vol-1',
                name: 'Root Volume',
                state: VolumeState.Ready,
                size: **********,
                diskkbsread: 500000,
                diskkbswrite: 500000,
                diskioread: 500000,
                diskiowrite: 500000,
                physicalsize: ***********,
                utilization: '75%',
                account: 'Account 1',
                domainid: 'id',
                domain: 'ROOT',
                zonename: 'zone 1',
                vmdisplayname: 'VM 1'
            } as Volume,
            {
                id: 'vol-2',
                name: 'Data Volume',
                state: VolumeState.Ready,
                size: ***********,
                diskioread: 60,
                diskiowrite: 60,
                diskkbsread: 60,
                diskkbswrite: 60,
                physicalsize: **********,
                utilization: '70%',
                account: 'Account 1',
                domainid: 'id',
                domain: 'Gorilla',
                zonename: 'zone 2',
                vmname: 'VM 1'
            } as Volume,
            {
                id: 'vol-4',
                name: 'Root Volume 2',
                state: VolumeState.UploadInProgress,
                size: *********,
                diskioread: 0,
                diskiowrite: null,
                diskkbsread: 0,
                diskkbswrite: 0,
                physicalsize: 0,
                utilization: '0%',
                account: 'Account 1',
                domainid: 'id',
                domain: 'ROOT',
                zonename: 'zone 1'
            } as Volume
        ];

        TestBed.configureTestingModule({
            imports: [VolumesComponent],
            providers: [
                provideMock(VolumesService),
                {
                    provide: ZoneDomainAccountStore,
                    useValue: zoneDomainAccountStoreMock,
                },
                provideMock(CloudInfraPermissionService),
                provideMock(VolumesPermissionService),
                provideMock(UserContextService),
            ]
        }).compileComponents();

        volumesServiceMock = TestBed.inject(VolumesService) as jasmine.SpyObj<VolumesService>;
        volumesServiceMock.getVolumes.and.returnValue(of(volumes));

        userContextServiceMock = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        volumesPermissionServiceMock = TestBed.inject(VolumesPermissionService) as jasmine.SpyObj<VolumesPermissionService>;
        cloudInfraPermissionServiceMock = TestBed.inject(CloudInfraPermissionService) as jasmine.SpyObj<CloudInfraPermissionService>;

        userContextServiceMock = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;

        userContextServiceMock.currentUser = {
            organizationId: 1,
            cloudInfraUserContext: {
                accountName: 'Account 1',
                domainId: 'id',
                hasMappedDomain: false,
            } as CloudInfraUserContext
        } as UserContext;

        volumesPermissionServiceMock.getVolumesPermissionsForCurrentUser.and.returnValue({
            listSnapshots: true,
            createVolume: true,
            uploadVolume: true,
            createSnapshot: true,
            createSnapshotPolicy: true,
            attachVolume: true,
            createTemplate: true,
            migrateVolume: true,
            destroyVolume: true,
            resizeVolume: true,
            extractVolume: true,
            detachVolume: true
        });

        cloudInfraPermissionServiceMock.isAdmin.and.returnValue(true);

        fixture = TestBed.createComponent(VolumesComponent);

        dataTableDebugElement = fixture.debugElement.query(By.directive(DatatableComponent));
        dataTable = dataTableDebugElement?.nativeElement;
    });

    describe('Component Initialization', () => {

        it('should load volumes data on initialization', () => {
            fixture.detectChanges();
            expect(volumesServiceMock.getVolumes).toHaveBeenCalledOnceWith('id', 'Account 1');
        });
    });

    describe('Data Display', () => {

        beforeEach(() => {
            fixture.detectChanges();
        });

        it('should display volumes in the data table', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            expect(rows.length).toBe(volumes.length);
        });

        it('should display correct volume data in table cells', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const firstRow = rows[0];
            const firstRowCells = firstRow.querySelectorAll('.datatable-body-cell');

            expect(firstRowCells.length).toBeGreaterThan(0);
        });

        it('should show the expected volume data in the grid by default', () => {
            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const firstRow = rows[0];
            const firstRowCells = firstRow.querySelectorAll('.datatable-body-cell');

            const secondRow = rows[1];
            const secondRowCells = secondRow.querySelectorAll('.datatable-body-cell');

            const thirdRow = rows[2];
            const thirdRowCells = thirdRow.querySelectorAll('.datatable-body-cell');

            expect(secondRowCells[0].textContent.trim()).toBe(volumes[0].state);
            expect(secondRowCells[1].textContent).toBe(volumes[0].name);
            expect(secondRowCells[2].textContent).toBe('VM 1');
            expect(secondRowCells[3].textContent.trim()).toBe('4.68 GB');
            expect(secondRowCells[4].textContent.trim()).toBe('18.64 GB');
            expect(secondRowCells[5].textContent.trim()).toBe('75%');

            expect(firstRowCells[0].textContent.trim()).toBe(volumes[1].state);
            expect(firstRowCells[1].textContent).toBe(volumes[1].name);
            expect(firstRowCells[2].textContent).toBe('VM 1');
            expect(firstRowCells[3].textContent.trim()).toBe('9.31 GB');
            expect(firstRowCells[4].textContent.trim()).toBe('4.25 GB');
            expect(firstRowCells[5].textContent.trim()).toBe('70%');

            expect(thirdRowCells[0].textContent.trim()).toBe(volumes[2].state);
            expect(thirdRowCells[1].textContent).toBe(volumes[2].name);
            expect(thirdRowCells[2].textContent).toBe('');
            expect(thirdRowCells[3].textContent.trim()).toBe('0.47 GB');
            expect(thirdRowCells[4].textContent.trim()).toBe('-');
            expect(thirdRowCells[5].textContent.trim()).toBe('0%');
        });

        it('should show the expected metrics data in the grid when selecting metrics in the toggle', () => {

            const toggle = fixture.debugElement.query(By.css('[data-testid="metrics-toggle"]')).nativeElement as HTMLButtonElement;
            toggle.click();
            fixture.detectChanges();

            const rows = dataTable.querySelectorAll('datatable-row-wrapper');
            const firstRow = rows[0];
            const firstRowCells = firstRow.querySelectorAll('.datatable-body-cell');

            const secondRow = rows[1];
            const secondRowCells = secondRow.querySelectorAll('.datatable-body-cell');

            const thirdRow = rows[2];
            const thirdRowCells = thirdRow.querySelectorAll('.datatable-body-cell');

            expect(secondRowCells[0].textContent.trim()).toBe(volumes[0].state);
            expect(secondRowCells[1].textContent).toBe(volumes[0].name);
            expect(secondRowCells[2].textContent).toBe('VM 1');
            expect(secondRowCells[3].textContent).toBe(volumes[0].zonename);
            expect(secondRowCells[4].textContent.trim()).toBe('500,000');
            expect(secondRowCells[5].textContent.trim()).toBe('500,000');
            expect(secondRowCells[6].textContent.trim()).toBe('1,000,000');

            expect(firstRowCells[0].textContent.trim()).toBe(volumes[1].state);
            expect(firstRowCells[1].textContent).toBe(volumes[1].name);
            expect(firstRowCells[2].textContent).toBe('VM 1');
            expect(firstRowCells[3].textContent).toBe(volumes[1].zonename);
            expect(firstRowCells[4].textContent.trim()).toBe('60');
            expect(firstRowCells[5].textContent.trim()).toBe('60');
            expect(firstRowCells[6].textContent.trim()).toBe('120');

            expect(thirdRowCells[0].textContent.trim()).toBe(volumes[2].state);
            expect(thirdRowCells[1].textContent).toBe(volumes[2].name);
            expect(thirdRowCells[2].textContent).toBe('');
            expect(thirdRowCells[3].textContent).toBe(volumes[2].zonename);
            expect(thirdRowCells[4].textContent.trim()).toBe('-');
            expect(thirdRowCells[5].textContent.trim()).toBe('-');
            expect(thirdRowCells[6].textContent.trim()).toBe('-');
        });

    });

});
