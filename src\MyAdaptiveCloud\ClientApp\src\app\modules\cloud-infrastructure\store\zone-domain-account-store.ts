import { computed, inject } from '@angular/core';
import { CloudInfrastructureRootDomainName } from '@app/shared/models/constants';
import { CloudInfrastructureSessionService } from '@app/shared/services/cloud-infrastructure-session.service';
import { UserContextService } from '@app/shared/services/user-context.service';
import { tapResponse } from '@ngrx/operators';
import { patchState, signalStore, withComputed, withHooks, withMethods, withState } from '@ngrx/signals';
import { rxMethod } from '@ngrx/signals/rxjs-interop';
import { EMPTY, forkJoin, pipe, switchMap } from 'rxjs';
import { CloudInfraAccountViewModel } from '../models/cloud-infra-account.view-model';
import { CloudInfraDomainViewModel } from '../models/cloud-infra-domain.view-model';
import { CloudInfraAccountService } from '../services/cloud-infra-account.service';
import { CloudInfraDomainService } from '../services/cloud-infra-domain.service';
import { CloudInfraZoneService } from '../services/cloud-infra-zone.service';
import { ZoneDomainAccountState } from './zone-domain-account-state';
import { updateAccountsByDomainId, updateSubDomainsByDomainId } from './zone-domain-account.helpers';

const initialState: ZoneDomainAccountState = {
    domains: [],
    accounts: [],
    selectedDomain: null,
    selectedAccount: null,
    zones: []
};

export const ZoneDomainAccountStore = signalStore(
    withState(initialState),
    withMethods((
        store,
        cloudInfraSessionService = inject(CloudInfrastructureSessionService),
        userContextService = inject(UserContextService),
        domainService = inject(CloudInfraDomainService),
        accountService = inject(CloudInfraAccountService),
        cloudInfraZoneService = inject(CloudInfraZoneService)
    ) => ({
        loadSubDomainsByDomainId: rxMethod<string>(pipe(switchMap(domainId => domainService.getDomainChildrenList(domainId)
            .pipe(tapResponse({
                next: res => {
                    const updatedDomains = updateSubDomainsByDomainId(domainId, store.domains(), res);
                    patchState(store, state => ({
                        ...state,
                        domains: [...updatedDomains]
                    }));
                },
                error: () => EMPTY
            }))))),
        loadAccountsByDomainId: rxMethod<string>(pipe(switchMap(domainId => accountService.getAccountsByDomainId(domainId)
            .pipe(tapResponse({
                next: res => {
                    const updatedDomains = updateAccountsByDomainId(domainId, store.domains(), res);
                    patchState(store, state => ({
                        ...state,
                        domains: [...updatedDomains]
                    }));
                },
                error: () => EMPTY
            }))))),

        // eslint-disable-next-line @typescript-eslint/no-invalid-void-type
        loadInitialData: rxMethod<void>(pipe(switchMap(() => cloudInfraSessionService.login()
            .pipe(switchMap(response => {
                if (!userContextService.currentUser.cloudInfraUserContext.hasMappedDomain) {
                    return forkJoin([
                        accountService.getAccountById(response.accountId),
                        cloudInfraZoneService.getZones()
                    ])
                        .pipe(tapResponse({
                            next: res => patchState(store, state => ({
                                ...state,
                                domains: [],
                                accounts: res[0],
                                selectedAccount: res[0].length ? res[0][0] : null,
                                zones: res[1]
                            })),
                            error: () => EMPTY
                        }));
                }
                return forkJoin([
                    domainService.getDomainList(response.domainId),
                    accountService.getAccountsByDomainId(response.domainId),
                    domainService.getDomainChildrenList(response.domainId),
                    cloudInfraZoneService.getZones()
                ])
                    .pipe(tapResponse({
                        next: res => {
                            let domains = res[0];
                            domains = updateAccountsByDomainId(response.domainId, domains, res[1]);
                            domains = updateSubDomainsByDomainId(response.domainId, domains, res[2]);
                            patchState(store, state => ({
                                ...state,
                                domains,
                                zones: res[3]
                            }));

                        },
                        error: () => EMPTY
                    }));
            })))))
    })),
    withMethods(store => ({
        setSelectedAccount(account: CloudInfraAccountViewModel): void {
            if (store.selectedAccount()?.id === account.id) {
                // Unselect the account if it is already selected
                patchState(store, state => ({ ...state, selectedAccount: null, selectedDomain: null }));
            } else {
                patchState(store, state => ({ ...state, selectedAccount: account, selectedDomain: null }));
            }
        },
        setSelectedDomain(domain: CloudInfraDomainViewModel): void {
            if (domain.id === store.selectedDomain()?.id) {
                // Unselect the domain if it is already selected
                patchState(store, state => ({ ...state, selectedDomain: null, selectedAccount: null }));
            } else {
                patchState(store, state => ({ ...state, selectedDomain: domain, selectedAccount: null }));
            }
        },
        onToggleDomain(domainId: string, isExpanded: boolean): void {
            function toggleDomain(domain: CloudInfraDomainViewModel): CloudInfraDomainViewModel {
                if (domain.id === domainId) {
                    const newIsExpanded = !isExpanded;

                    if (newIsExpanded) {
                        // Load subdomains if they exist but are not yet loaded
                        if (domain.hasChild && !domain.subDomains?.length) {
                            store.loadSubDomainsByDomainId(domain.id);
                        }

                        // Load accounts if they are not yet loaded
                        if (!domain.accounts?.length) {
                            store.loadAccountsByDomainId(domain.id);
                        }
                    }
                    return { ...domain, isExpanded: newIsExpanded };
                }

                // Check and update subdomains recursively
                if (domain.subDomains?.length) {
                    return {
                        ...domain,
                        subDomains: domain.subDomains.map(toggleDomain),
                    };
                }

                return domain;
            }

            const updatedDomains = store.domains().map(toggleDomain);
            patchState(store, { domains: updatedDomains });
        }
    })),
    withComputed((store, userContextService = inject(UserContextService)) => ({
        isRootDomainSelected: computed(() => store.selectedDomain()?.level === 0),
        mainDomain: computed(() => (store.domains()?.length ? store.domains()[0] : null)),
        domainOrAccountSelected: computed(() => store.selectedDomain() || store.selectedAccount()),
        getDomainId: computed(() => {
            const selectedDomainId = store.selectedAccount() ? store.selectedAccount()?.domainId : store.selectedDomain()?.id;
            const domainId = selectedDomainId ? selectedDomainId :
                store.domains()[0].name === CloudInfrastructureRootDomainName ? null : store.domains()[0].id;
            return domainId ?? userContextService.currentUser.cloudInfraUserContext.domainId;
        }),
        getAccount: computed(() => {
            const account = store.selectedAccount() ? store.selectedAccount()?.name : null;
            return account;
        }),
    })),
    withHooks({
        onInit(store) {
            store.loadInitialData();
        }
    })
);

