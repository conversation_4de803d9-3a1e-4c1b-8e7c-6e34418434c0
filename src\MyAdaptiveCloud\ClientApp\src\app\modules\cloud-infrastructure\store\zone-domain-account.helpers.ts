import { CloudInfraAccountViewModel } from '../models/cloud-infra-account.view-model';
import { CloudInfraDomainViewModel } from '../models/cloud-infra-domain.view-model';

/**
 * Recursive method to find a domain by its ID.
 */
export const findDomainById = (domainId: string, domains: CloudInfraDomainViewModel[]): CloudInfraDomainViewModel | null => {
    for (const domain of domains) {
        if (domain.id === domainId) {
            return domain;
        }
        if (domain.hasChild && domain.subDomains?.length) {
            const result = findDomainById(domainId, domain.subDomains);
            if (result) {
                return result;
            }
        }
    }
    return null;
};

/**
 * Helper function to create a deep copy of a domain.
 */
const deepCopyDomain = (domain: CloudInfraDomainViewModel): CloudInfraDomainViewModel => ({
    ...domain,
    subDomains: domain.subDomains ? domain.subDomains.map(deepCopyDomain) : [],
    accounts: domain.accounts ? [...domain.accounts] : []
});

/**
 * Recursive method to update a domain by its ID with new data.
 */
const updateDomainById = (
    domainId: string,
    domains: CloudInfraDomainViewModel[],
    updateFn: (domain: CloudInfraDomainViewModel) => void
): boolean => {
    for (const domain of domains) {
        if (domain.id === domainId) {
            updateFn(domain);
            return true; // Indicate success
        }
        if (domain.subDomains?.length) {
            const updated = updateDomainById(domainId, domain.subDomains, updateFn);
            if (updated) {
                return true; // Propagate success
            }
        }
    }
    return false; // Domain not found
};

/**
 * Updates the subdomains of a domain by its ID.
 */
export const updateSubDomainsByDomainId = (
    domainId: string,
    domains: CloudInfraDomainViewModel[],
    newSubDomains: CloudInfraDomainViewModel[]
): CloudInfraDomainViewModel[] => {
    const domainsCopy = domains.map(deepCopyDomain);

    updateDomainById(domainId, domainsCopy, domain => {
        domain.subDomains = newSubDomains.map(deepCopyDomain);
    });

    return domainsCopy;
};

/**
 * Updates the accounts of a domain by its ID.
 */
export const updateAccountsByDomainId = (
    domainId: string,
    domains: CloudInfraDomainViewModel[],
    newAccounts: CloudInfraAccountViewModel[]
): CloudInfraDomainViewModel[] => {
    const domainsCopy = domains.map(deepCopyDomain);

    updateDomainById(domainId, domainsCopy, domain => {
        domain.accounts = newAccounts.filter(account => account.domainId === domainId);
        domain.isExpanded = true; // Ensure the domain is expanded after updating accounts
    });

    return domainsCopy;
};

