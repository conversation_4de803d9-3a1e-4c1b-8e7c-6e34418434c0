import { TestBed } from '@angular/core/testing';
import { CloudInfraUserContext } from '@app/shared/models/cloud-infra-user-context';
import { UserContext } from '@app/shared/models/user-context.model';
import { UserContextService } from '@app/shared/services/user-context.service';
import { provideMock } from '@app/shared/specs/spy-helpers';
import { NETWORKING_ENDPOINT_NAMES } from '../modules/networking/models/networking.constants';
import { CloudInfraPermissionService } from './cloud-infra-permission.service';

describe('CloudInfraPermissionService', () => {

    let service: CloudInfraPermissionService;
    let mockUserContextService: jasmine.SpyObj<UserContextService>;
    let mockUserContext: UserContext;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                CloudInfraPermissionService,
                provideMock(UserContextService)
            ]
        });

        mockUserContext = {
            cloudInfraUserContext: {
                permissions: [],
                roleType: 'User',
                roleName: 'User'
            } as CloudInfraUserContext
        } as UserContext;

        mockUserContextService = TestBed.inject(UserContextService) as jasmine.SpyObj<UserContextService>;
        mockUserContextService.currentUser = mockUserContext;
        service = TestBed.inject(CloudInfraPermissionService);
    });

    describe('isAdmin', () => {

        it('should return false when user role type is "User"', () => {
            mockUserContext.cloudInfraUserContext.roleType = 'User';

            const result = service.isAdmin();

            expect(result).toBeFalse();
        });

        it('should return true when user role type is not "User"', () => {
            mockUserContext.cloudInfraUserContext.roleType = 'Admin';

            const result = service.isAdmin();

            expect(result).toBeTrue();
        });

        it('should return true when user role type is "DomainAdmin"', () => {
            mockUserContext.cloudInfraUserContext.roleType = 'DomainAdmin';

            const result = service.isAdmin();

            expect(result).toBeTrue();
        });

    });

    describe('isRootAdmin', () => {

        it('should return false when user is not admin', () => {
            mockUserContext.cloudInfraUserContext.roleType = 'User';
            mockUserContext.cloudInfraUserContext.roleName = 'User';

            const result = service.isRootAdmin();

            expect(result).toBeFalse();
        });

        it('should return false when user is admin but not root admin', () => {
            mockUserContext.cloudInfraUserContext.roleType = 'Admin';
            mockUserContext.cloudInfraUserContext.roleName = 'Domain Admin';

            const result = service.isRootAdmin();

            expect(result).toBeFalse();
        });

        it('should return true when user is admin and role name is "Root Admin"', () => {
            mockUserContext.cloudInfraUserContext.roleType = 'Admin';
            mockUserContext.cloudInfraUserContext.roleName = 'Root Admin';

            const result = service.isRootAdmin();

            expect(result).toBeTrue();
        });

    });

    describe('hasAllPermissions', () => {

        it('should return false when permissions array is empty', () => {
            mockUserContext.cloudInfraUserContext.permissions = [];

            const result = service.hasAllPermissions();

            expect(result).toBeFalse();
        });

        it('should return false when permissions contains multiple specific permissions', () => {
            mockUserContext.cloudInfraUserContext.permissions = [
                NETWORKING_ENDPOINT_NAMES.listNetworks,
                NETWORKING_ENDPOINT_NAMES.createNetwork
            ];

            const result = service.hasAllPermissions();

            expect(result).toBeFalse();
        });

        it('should return true when permissions contains only "*"', () => {
            mockUserContext.cloudInfraUserContext.permissions = ['*'];

            const result = service.hasAllPermissions();

            expect(result).toBeTrue();
        });

    });

    describe('hasPermission', () => {

        it('should return false when permission is not in the permissions array', () => {
            mockUserContext.cloudInfraUserContext.permissions = [
                NETWORKING_ENDPOINT_NAMES.listNetworks,
                NETWORKING_ENDPOINT_NAMES.restartNetwork
            ];

            const result = service.hasPermission(NETWORKING_ENDPOINT_NAMES.createNetwork);

            expect(result).toBeFalse();
        });

        it('should return true when permission is in the permissions array', () => {
            mockUserContext.cloudInfraUserContext.permissions = [
                NETWORKING_ENDPOINT_NAMES.listNetworks,
                NETWORKING_ENDPOINT_NAMES.createNetwork,
                NETWORKING_ENDPOINT_NAMES.restartNetwork
            ];

            const result = service.hasPermission(NETWORKING_ENDPOINT_NAMES.createNetwork);

            expect(result).toBeTrue();
        });

        it('should return true when checking listNetworks permission', () => {
            mockUserContext.cloudInfraUserContext.permissions = [
                NETWORKING_ENDPOINT_NAMES.listNetworks
            ];

            const result = service.hasPermission(NETWORKING_ENDPOINT_NAMES.listNetworks);

            expect(result).toBeTrue();
        });

        it('should return true when checking deleteNetwork permission', () => {
            mockUserContext.cloudInfraUserContext.permissions = [
                NETWORKING_ENDPOINT_NAMES.deleteNetwork,
                NETWORKING_ENDPOINT_NAMES.editNetwork
            ];

            const result = service.hasPermission(NETWORKING_ENDPOINT_NAMES.deleteNetwork);

            expect(result).toBeTrue();
        });

        it('should return true when checking editNetwork permission', () => {
            mockUserContext.cloudInfraUserContext.permissions = [
                NETWORKING_ENDPOINT_NAMES.editNetwork
            ];

            const result = service.hasPermission(NETWORKING_ENDPOINT_NAMES.editNetwork);

            expect(result).toBeTrue();
        });

        it('should return true when checking VPN related permissions', () => {
            mockUserContext.cloudInfraUserContext.permissions = [
                NETWORKING_ENDPOINT_NAMES.listVpnUsers,
                NETWORKING_ENDPOINT_NAMES.addVpnUser,
                NETWORKING_ENDPOINT_NAMES.deleteVpnUser
            ];

            expect(service.hasPermission(NETWORKING_ENDPOINT_NAMES.listVpnUsers)).toBeTrue();
            expect(service.hasPermission(NETWORKING_ENDPOINT_NAMES.addVpnUser)).toBeTrue();
            expect(service.hasPermission(NETWORKING_ENDPOINT_NAMES.deleteVpnUser)).toBeTrue();
        });

        it('should return true when checking VPC related permissions', () => {
            mockUserContext.cloudInfraUserContext.permissions = [
                NETWORKING_ENDPOINT_NAMES.listVirtualPrivateClouds,
                NETWORKING_ENDPOINT_NAMES.createVirtualPrivateCloud,
                NETWORKING_ENDPOINT_NAMES.editVirtualPrivateCloud,
                NETWORKING_ENDPOINT_NAMES.deleteVirtualPrivateCloud
            ];

            expect(service.hasPermission(NETWORKING_ENDPOINT_NAMES.listVirtualPrivateClouds)).toBeTrue();
            expect(service.hasPermission(NETWORKING_ENDPOINT_NAMES.createVirtualPrivateCloud)).toBeTrue();
            expect(service.hasPermission(NETWORKING_ENDPOINT_NAMES.editVirtualPrivateCloud)).toBeTrue();
            expect(service.hasPermission(NETWORKING_ENDPOINT_NAMES.deleteVirtualPrivateCloud)).toBeTrue();
        });

        it('should return true when checking VPN Gateway related permissions', () => {
            mockUserContext.cloudInfraUserContext.permissions = [
                NETWORKING_ENDPOINT_NAMES.listRemoteVpnGateways,
                NETWORKING_ENDPOINT_NAMES.createRemoteVpnGateway,
                NETWORKING_ENDPOINT_NAMES.editRemoteVpnGateway,
                NETWORKING_ENDPOINT_NAMES.deleteRemoteVpnGateway
            ];

            expect(service.hasPermission(NETWORKING_ENDPOINT_NAMES.listRemoteVpnGateways)).toBeTrue();
            expect(service.hasPermission(NETWORKING_ENDPOINT_NAMES.createRemoteVpnGateway)).toBeTrue();
            expect(service.hasPermission(NETWORKING_ENDPOINT_NAMES.editRemoteVpnGateway)).toBeTrue();
            expect(service.hasPermission(NETWORKING_ENDPOINT_NAMES.deleteRemoteVpnGateway)).toBeTrue();
        });

        it('should return false when permissions array is empty', () => {
            mockUserContext.cloudInfraUserContext.permissions = [];

            const result = service.hasPermission(NETWORKING_ENDPOINT_NAMES.listNetworks);

            expect(result).toBeFalse();
        });

    });

});
