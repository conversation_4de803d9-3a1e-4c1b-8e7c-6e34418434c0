import { FormControl } from '@angular/forms';

export interface NetworkDetailForm {
  name: FormControl<string>;
  description: FormControl<string>;
  cidr: FormControl<string>;
  networkDomain: FormControl<string>;
  zoneName: FormControl<string>;
  domain: FormControl<string>;
  account: FormControl<string>;
  type: FormControl<string>;
  id: FormControl<string>;
  networkOfferingId: FormControl<string>;
  restartrequired: FormControl<string>;
  redundantrouter: FormControl<string>;
}
