using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using MyAdaptiveCloud.Core.Common;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Contexts;
using MyAdaptiveCloud.Services.Apis.CloudStack.Model;
using MyAdaptiveCloud.Services.Apis.CloudStack.Requests;
using MyAdaptiveCloud.Services.Apis.CloudStack.Responses;
using MyAdaptiveCloud.Services.Exceptions;
using MyAdaptiveCloud.Services.Services;
using System.Net;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace MyAdaptiveCloud.Services.Apis.CloudStack
{
    public class CloudStackApi : ICloudStackApi
    {
        private Domain _rootDomain = null;
        private readonly HttpClient _httpClient;
        private readonly IMemoryCache _cache;
        private readonly MyAdaptiveCloudContext _dbContext;
        private readonly ILogger _logger;
        private readonly IConfigurationService _configurationService;

        private readonly JsonSerializerOptions _jsonSerializerOptions = new()
        {
            PropertyNameCaseInsensitive = true
        };

        public CloudStackApi(HttpClient client, IConfigurationService configurationService, MyAdaptiveCloudContext context, IMemoryCache cache, ILogger<CloudStackApi> logger)
        {
            var config = configurationService.GetAdaptiveCloudApiConfiguration().GetAwaiter().GetResult();

            client.BaseAddress = !string.IsNullOrEmpty(config.CSBaseUrl) ? new Uri(config.CSBaseUrl) : null;
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            _httpClient = client;
            _cache = cache;
            _dbContext = context;
            _logger = logger;
            _configurationService = configurationService;
        }

        public async Task<List<AccountResponse>> GetAllAccounts()
        {
            Dictionary<string, string> requestParams = new()
            {
                { "command", "listAccounts" },
                { "details", "all" },
                { "isrecursive", "false" },
                { "listall", "true" }
            };
            var res = await GetResults<ListAccountsResponse, AccountResponse>(requestParams);
            return res.Items;
        }

        public async Task<List<AccountResponse>> GetAccountsForDomainId(Guid domainId)
        {
            Dictionary<string, string> requestParams = new()
            {
                { "command", "listAccounts" },
                { "details", "all" },
                { "isrecursive", "false" },
                { "listall", "false" }, // This appears to be a CloudStack Api defect. This overrides the domainId and returns all accounts, so use false
                { "domainid", domainId.ToString() }
            };
            var res = await GetResults<ListAccountsResponse, AccountResponse>(requestParams);
            return res.Items;
        }

        public async Task<Domain> GetRootDomain()
        {
            if (_rootDomain == null)
            {
                var domains = await GetDomains(new DomainRequest { Id = null, Level = 0 });
                _rootDomain = domains.Count >= 1 ? domains[0] : null;
            }

            return _rootDomain;
        }

        public async Task<List<AccountResponse>> GetAccounts()
        {
            // Fetch the root domain
            Domain rootDomain = await GetRootDomain();
            List<AccountResponse> accounts = new List<AccountResponse>();
            if (rootDomain != null)
            {
                accounts = await GetAccountsForDomainId(rootDomain.Id);
            }

            return accounts;
        }

        public async Task<AccountResponse> GetAccountById(Guid accountId)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listAccounts" },
                { "details", "all" },
                { "isrecursive", "false" },
                { "id", accountId.ToString() },
                { "listall", "true" },
            };
            var res = await GetResults<ListAccountsResponse, AccountResponse>(requestParams);
            return res.Items.Count > 0 ? res.Items[0] : null;
        }

        public async Task<Domain> GetDomainById(Guid domainId)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listDomains" },
                { "details", "all" },
                { "isrecursive", "false" },
                { "id", domainId.ToString() },
                { "listall", "true" },
            };
            var res = await GetResults<ListDomainsResponse, Domain>(requestParams);
            return res.Items.Count > 0 ? res.Items[0] : null;
        }

        public async Task<List<Domain>> GetDomains(DomainRequest domainRequest)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listDomains" },
                { "details", "all" },
                { "isrecursive", "false" },
                { "listall", "true" },
            };
            if (domainRequest.Id.HasValue)
            {
                requestParams.Add("id", domainRequest.Id.Value.ToString());
            }

            if (domainRequest.Level.HasValue)
            {
                requestParams.Add("level", domainRequest.Level.Value.ToString());
            }

            if (!string.IsNullOrEmpty(domainRequest.Name))
            {
                requestParams.Add("name", domainRequest.Name);
            }

            var res = await GetResults<ListDomainsResponse, Domain>(requestParams);
            return res.Items;
        }

        public async Task<List<Domain>> GetDomainChildren(Guid? domainId = null)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listDomainChildren" },
                { "details", "all" },
                { "isrecursive", "false" },
                { "listall", "false" },
            };
            if (domainId != null)
            {
                requestParams.Add("id", domainId.Value.ToString());
            }

            var res = await GetResults<ListDomainChildrenResponse, Domain>(requestParams);
            return res.Items;
        }

        public async Task<(int, List<VirtualMachine>)> GetVirtualMachines(Guid? domainId = null, int? pageSize = null, int? pageNumber = null)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listVirtualMachines" },
                { "isrecursive", "false" },
                { "listall", "true" },
            };

            if (domainId != null)
            {
                requestParams.Add("id", domainId.Value.ToString());
            }

            if (pageSize != null && pageNumber != null)
            {
                requestParams.Add("page", pageNumber.ToString());
                requestParams.Add("pagesize", pageSize.ToString());
            }

            var res = await GetResults<ListVirtualMachinesResponse, VirtualMachine>(requestParams);
            return (res.Count, res.Items);
        }

        public async Task<(int, List<VirtualMachine>)> GetVirtualMachinesByDomainId(Guid domainId)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listVirtualMachines" },
                { "isrecursive", "false" },
                { "listall", "true" },
                { "domainid", domainId.ToString() }
            };

            var res = await GetResults<ListVirtualMachinesResponse, VirtualMachine>(requestParams);
            return (res.Count, res.Items);
        }

        public async Task<PublicIpAddress> GetPublicIPAddress(Guid domainId, string publicIPAddress)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listPublicIpAddresses" },
                { "domainid", domainId.ToString() },
                { "isrecursive", "true" },
                { "ipaddress", publicIPAddress },
            };

            var res = await GetResults<ListPublicIPAddressesResponse, PublicIpAddress>(requestParams);
            return res.Items.FirstOrDefault();
        }

        public async Task<List<OsCategory>> GetOsCategories()
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listOsCategories" },
                { "details", "all" },
                { "isrecursive", "false" },
                { "listall", "true" },
            };

            var res = await GetResults<ListOsCategoriesResponse, OsCategory>(requestParams);
            return res.Items;
        }

        public async Task<List<OsType>> GetOsTypes(Guid? osCategoryId = null)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listOsTypes" },
            };
            if (osCategoryId != null)
            {
                requestParams.Add("oscategoryid", osCategoryId.Value.ToString());
            }

            var res = await GetResults<ListOsTypesResponse, OsType>(requestParams);
            return res.Items;
        }

        public async Task<Domain> CreateDomain(Domain domain)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "createDomain" },
                { "name", domain.Name },
            };
            // This SHOULD be passed in as the interpolation of $"{domain.Name}.internal since this is our standard"
            if (!string.IsNullOrEmpty(domain.NetworkDomain))
            {
                requestParams.Add("networkdomain", domain.NetworkDomain);
            }

            // This will default to having ROOT as its parent if unset
            if (domain.ParentDomainId.HasValue)
            {
                requestParams.Add("parentdomainid", domain.ParentDomainId.ToString());
            }

            var response = await ExecutePostRequest(requestParams);
            var domainResponse = await GetDeserializedResponse<CreateDomainResponse>(response);
            return domainResponse.domain;
        }

        public async Task<DeleteResponse> RemoveDomain(Domain domain, bool cleanup = true)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "deleteDomain" },
                { "id", domain.Id.ToString() },
                { "cleanup", cleanup ? "true" : "false" },
            };

            var response = await ExecutePostRequest(requestParams);
            var removeDomainResponse = await GetDeserializedResponse<DeleteResponse>(response);
            return removeDomainResponse;
        }

        public string GetRandomPass(int pwdlen = 16)
        {
            const string inputChars = "ABCDEFGHJKLMNOPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz0123456789";
            //const string inputChars = "ABCDEFGHJKLMNOPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz0123456789!#$%&'()*+,-./:;<=>?@_|";
            int upperBound = inputChars.Length - 1;
            char[] pwd = new char[pwdlen];

            for (int idx = 0; idx < pwdlen; idx++)
            {
                RandomNumberGenerator.GetInt32(upperBound);
                pwd[idx] = inputChars[RandomNumberGenerator.GetInt32(upperBound)];
            }

            return new string(pwd);
        }

        public async Task<AccountResponse> CreateAccount(CreateAccountRequest account)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "createAccount" },
                // Username must be the same as email, since our SSO uses that
                { "username", account.Username },
                { "email", account.Email },
                { "firstname", account.Firstname },
                { "lastname", account.Lastname },
                // Plain text password. Since we're using SSO, just do a random for this, as it is required by CloudStack
                { "password", GetRandomPass() },
                // This should likely be their CW Company name?
                { "account", account.Account },
                { "accounttype", ((int)account.Accounttype).ToString() }
            };
            // This should be the UUID of the newly created domain for partner
            // Or the partner domain for Suborganizations (phase 2)
            // Or null for ROOT (or the ROOT domainId)
            if (account.Domainid.HasValue)
            {
                requestParams.Add("domainid", account.Domainid.ToString());
            }

            // This SHOULD be passed in as the interpolation of $"{account.Name}.internal" since this is our standard. However, it cannot
            // have spaces in it, so this may need to be left out or discussed.
            if (account.Networkdomain != null)
            {
                requestParams.Add("networkdomain", account.Networkdomain);
            }

            // TODO Figure out the proper format for this, maybe we just always set it to CDT, UTC, or leave blank?
            if (account.Timezone != null)
            {
                requestParams.Add("timezone", account.Timezone);
            }

            var response = await ExecutePostRequest(requestParams);

            if (!response.IsSuccessStatusCode)
            {
                var errorMessage = ExtractXDescription(response);
                _logger.LogError("Cloud Stack API - Create Account Error: " + errorMessage);
                throw new BadRequestException(errorMessage);
            }

            var acctResponse = await GetDeserializedResponse<CreateAccountResponse>(response);
            return acctResponse.account;
        }

        public async Task<List<Model.User>> GetUsersByUsername(string username)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listUsers" },
                { "listall", "true" }, ///< Make sure that we have this set so that it will return all users across all accounts
                { "username", username },
            };

            var res = await GetResults<GetUsersResponse, User>(requestParams);
            return res.Items;
        }

        public async Task<SamlAuthorization> GetSamlAuthorization(Guid userId)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listSamlAuthorization" },
                { "username", userId.ToString() },
            };

            var res = await GetResults<ListSamlAuthorizationResponse, SamlAuthorization>(requestParams);
            return res.Items.Count > 0 ? res.Items[0] : null;
        }

        public async Task AuthorizeSamlSso(Guid userId, bool authorized, Idp idpId)
        {
            if (authorized && idpId == null)
            {
                throw new ArgumentException("Must include the idpId when enabling SSO", nameof(idpId));
            }

            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "authorizeSamlSso" },
                { "userid", userId.ToString() },
                { "enable", authorized.ToString() },
            };
            if (idpId != null)
            {
                requestParams.Add("entityid", idpId.Id);
            }

            await ExecutePostRequest(requestParams);
        }

        public async Task<Idp> GetIdp()
        {
            var idp = _cache.GetOrCreate(CacheEntryKeys.CloudStackApiGetIdpKey, async entry =>
            {
                entry.AbsoluteExpiration = new DateTimeOffset(DateTime.UtcNow).AddHours(1);
                Dictionary<string, string> requestParams = new Dictionary<string, string>
                {
                    { "command", "listIdps" },
                };

                var res = await GetResults<ListIdpsResponse, Idp>(requestParams);
                return res.Items.Count > 0 ? res.Items[0] : null;
            });
            return await idp;
        }

        public async Task<HttpResponseMessage> UpdateResourceLimit(Guid domainId, string accountName, Enum resourceType, int limit)
        {
            var resourceTypeIntegerValue = Convert.ToInt32(resourceType);

            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "updateResourceLimit" },
                { "domainid", domainId.ToString() },
                { "resourcetype", resourceTypeIntegerValue.ToString() },
                { "max", limit.ToString() },
            };
            if (!string.IsNullOrEmpty(accountName))
            {
                requestParams.Add("account", accountName);
            }

            return await ExecutePostRequest(requestParams);
        }

        public async Task<List<ResourceLimit>> GetResourceLimitsByDomainId(Guid domainId)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listResourceLimits" },
                { "domainid", domainId.ToString() },
            };

            var res = await GetResults<ListResourceLimitsResponse, ResourceLimit>(requestParams);
            return res.Items;
        }

        public async Task<List<ResourceLimit>> GetResourceLimitsByAccountName(Guid domainId, string accountName)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listResourceLimits" },
                { "domainid", domainId.ToString() },
                { "account", accountName },
            };

            var res = await GetResults<ListResourceLimitsResponse, ResourceLimit>(requestParams);
            return res.Items;
        }

        public async Task<List<Configuration>> GetGlobalConfigurationSettings(string configurationName)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listConfigurations" },
                { "name", configurationName }
            };

            var httpResponseMessage = await ExecuteGetRequest(requestParams);

            var res = await GetDeserializedResponse<ListConfigurationsResponse>(httpResponseMessage);

            return res.Items;
        }

        public async Task<HttpResponseMessage> UpdateAccountName(Guid accountId, string newAccountName)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "updateAccount" },
                { "id", accountId.ToString() },
            };
            if (!string.IsNullOrEmpty(newAccountName))
            {
                requestParams.Add("newname", newAccountName);
            }

            return await ExecutePostRequest(requestParams);
        }

        public async Task<HttpResponseMessage> DeleteUser(string userId)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "deleteUser" },
                { "id", userId.ToString() },
            };

            return await ExecutePostRequest(requestParams);
        }


        public async Task<HttpResponseMessage> MoveUser(string userId, string account, string accountId)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "moveUser" },
                { "id", userId.ToString() },
                { "account", account },
                { "accountId", accountId }
            };

            return await ExecutePostRequest(requestParams);
        }

        public async Task<UserResponse> CreateUser(CreateUser user)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "createUser" },
                { "account", user.Account },
                { "username", user.UserName },
                { "email", user.Email },
                { "firstname", user.FirstName },
                { "lastname", user.LastName },
                { "password", GetRandomPass() },
                { "domainid", user.DomainId.ToString() }
            };

            if (user.Timezone != null)
            {
                requestParams.Add("timezone", user.Timezone);
            }

            var response = await ExecutePostRequest(requestParams);

            if (!response.IsSuccessStatusCode)
            {
                var errorMessage = ExtractXDescription(response);
                _logger.LogError("Cloud Stack API - Create User Error - DUPLICATED ENTRY: " + errorMessage);
                throw new BadRequestException(errorMessage);
            }

            var userResponse = await GetDeserializedResponse<CreateUserResponse>(response);
            return userResponse.User;
        }

        public async Task<List<Model.User>> GetUsersByAccount(string accountId, string domainId)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listUsers" },
                { "account", accountId },
                { "domainid", domainId },
                { "isrecursive", "false" },
                { "listall", "false" },
            };

            var res = await GetResults<GetUsersResponse, User>(requestParams);
            return res.Items;
        }

        public async Task<List<Model.User>> GetUsersByDomain(string domainId)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listUsers" },
                { "domainid", domainId },
                { "isrecursive", "true" },
                { "listall", "true" },
            };

            var res = await GetResults<GetUsersResponse, User>(requestParams);
            return res.Items;
        }

        public async Task<List<Model.User>> GetUsersByEmail(string email)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listUsers" },
                { "username", email },
                { "isrecursive", "true" },
                { "listall", "true" },
            };

            var res = await GetResults<GetUsersResponse, User>(requestParams);
            return res.Items;
        }

        public async Task<UserKeys> GetUserKeys(Guid userId)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "getUserKeys" },
                { "id", userId.ToString() },
            };

            var httpResponseMessage = await ExecuteGetRequest(requestParams);
            var res = await GetDeserializedResponse<GetUserKeysResponse>(httpResponseMessage);
            return res.UserKeys;
        }

        public async Task<User> GetUser(string userApiKey)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "getUser" },
                { "userapikey", userApiKey },
            };

            var httpResponseMessage = await ExecuteGetRequest(requestParams);
            var res = await GetDeserializedResponse<GetUserResponse>(httpResponseMessage);
            return res.User;
        }

        public async Task<List<RolePermission>> GetRolePermissions(Guid roleId)
        {
            var requestParams = new Dictionary<string, string>
            {
                { "command", "listRolePermissions" },
                { "roleId", roleId.ToString() },
            };

            var httpResponseMessage = await ExecuteGetRequest(requestParams);
            var res = await GetDeserializedResponse<GetRolePermissionsReponse>(httpResponseMessage);
            return res.Items;
        }

        public async Task<UserKeys> RegisterUserKeys(Guid userId)
        {
            var requestParams = new Dictionary<string, string>
            {
                { "command", "registerUserKeys" },
                { "id", userId.ToString() },
            };

            var httpResponseMessage = await ExecuteGetRequest(requestParams);
            var res = await GetDeserializedResponse<GetUserKeysResponse>(httpResponseMessage);
            return res.UserKeys;
        }

        public async Task<List<Network>> GetNetworks()
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listNetworks" },
            };

            var res = await GetResults<ListNetworksResponse, Network>(requestParams);
            return res.Items;
        }

        public async Task<Network> GetNetwork(Guid id)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listNetworks" },
                { "id", id.ToString() },
                { "listall", "true" }
            };

            var res = await GetResults<ListNetworksResponse, Network>(requestParams);
            return res.Items?.First();
        }

        public async Task<List<Network>> GetNetworksByAccount(string accountId)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listNetworks" },
                { "accountid", accountId },
                { "listall", "true" }
            };

            var res = await GetResults<ListNetworksResponse, Network>(requestParams);
            return res.Items;
        }

        public async Task<List<Network>> GetNetworksByDomain(string domainId)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listNetworks" },
                { "domainid", domainId },
                { "listall", "true" }
            };

            var res = await GetResults<ListNetworksResponse, Network>(requestParams);
            return res.Items;
        }

        public async Task<List<Network>> GetNetworksByDomainAndAccount(string domainId, string accountName)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listNetworks" },
                { "domainid", domainId },
                { "account", accountName },
                { "listall", "true" }
            };

            var res = await GetResults<ListNetworksResponse, Network>(requestParams);
            return res.Items;
        }

        public async Task<List<IpReservation>> GetIpReservations(Guid networkId)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "listIpReservations" },
                { "networkid", networkId.ToString() }
            };

            var res = await GetResults<ListIpReservation, IpReservation>(requestParams);
            return res.Items;
        }

        public async Task<IpReservation> GenerateIpReservation(string networkId)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "generateIpReservation" },
                { "networkid", networkId }
            };
            var httpResponseMessage = await ExecutePostRequest(requestParams);
            var res = await GetDeserializedResponse<GenerateIpReservation>(httpResponseMessage);
            return res.IpReservation;
        }

        public async Task<IpReservation> CreateIpReservation(string networkId, string startIp, string endIp)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "generateIpReservation" },
                { "networkid", networkId },
                { "startIp", startIp },
                { "endIp", endIp }
            };
            var res = await GetDeserializedResponse<GenerateIpReservation>(await ExecutePostRequest(requestParams));
            return res.IpReservation;
        }

        public async Task<Boolean> RemoveIpReservation(string id)
        {
            Dictionary<string, string> requestParams = new Dictionary<string, string>
            {
                { "command", "removeIpReservation" },
                { "id", id }
            };
            var res = await ExecutePostRequest(requestParams);
            return res.IsSuccessStatusCode;
        }

        private async Task<HttpResponseMessage> ExecuteGetRequest(IDictionary<string, string> parameters)
        {
            var request = await BuildRequestDictionary(parameters);
            string relativeUriString = QueryHelpers.AddQueryString("", request);
            var response = await _httpClient.GetAsync(relativeUriString);
            return response;
        }

        private async Task<HttpResponseMessage> ExecutePostRequest(IDictionary<string, string> parameters)
        {
            var request = await BuildRequestDictionary(parameters);
            return await _httpClient.PostAsync("", new FormUrlEncodedContent(request));
        }

        private async Task<SortedDictionary<string, string>> BuildRequestDictionary(IDictionary<string, string> parameters)
        {
            var requestDictionary = new SortedDictionary<string, string>(parameters)
            {
                { "response", "json" }
            };

            var config = await _configurationService.GetAdaptiveCloudApiConfiguration();

            var apiKey = config.CSApiKey;

            if (!string.IsNullOrEmpty(apiKey))
            {
                requestDictionary.Add("apiKey", apiKey);
            }

            var secret = config.CSSecretKey;

            if (!string.IsNullOrEmpty(secret))
            {
                requestDictionary.Add("signature", GetSignature(requestDictionary, secret));
            }

            return requestDictionary;
        }

        private static string GetSignature(IDictionary<string, string> requestParams, string secret)
        {
            string hashable = null;
            foreach (var keyValuePair in requestParams)
            {
                string valueToEscape = keyValuePair.Value ?? string.Empty; // Use an empty string if the value is null
                hashable += (hashable != null ? "&" : "") + keyValuePair.Key.ToLower() + "=" + Uri.EscapeDataString(valueToEscape).ToLower();
            }

            Console.WriteLine(hashable);
            var hmac = new HMACSHA1(Encoding.UTF8.GetBytes(secret));
            byte[] hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(hashable));
            return Convert.ToBase64String(hash);
        }

        private async Task<T> GetResults<T, U>(IDictionary<string, string> parameters) where T : CloudStackListResponse<U>, new()
        {
            const int pageSize = 500;
            parameters["pagesize"] = pageSize.ToString();
            var pageNum = 1;
            bool moreResults;
            T res = new();
            do
            {
                parameters["page"] = pageNum.ToString();
                var request = await BuildRequestDictionary(parameters);
                string relativeUriString = QueryHelpers.AddQueryString("", request);
                var response = await _httpClient.GetAsync(relativeUriString);
                var results = await GetDeserializedResponse<T>(response);
                if (results.Count > 0)
                {
                    if (res.Items == null)
                    {
                        res.Items = results.Items;
                        res.Count = results.Items.Count;
                    }
                    else
                    {
                        res.Items.AddRange(results.Items);
                        res.Count += results.Items.Count;
                    }
                }

                moreResults = results.Count > pageSize * pageNum;
                pageNum++;
            } while (moreResults);

            return res;
        }

        private async Task<T> GetDeserializedResponse<T>(HttpResponseMessage response) where T : new()
        {
            if (!response.IsSuccessStatusCode
                && response.StatusCode != HttpStatusCode.RequestHeaderFieldsTooLarge
                && response.StatusCode != HttpStatusCode.Unauthorized) // Explicitly intercept Cloud API authentication failures to prevent direct exposure to MYAC users
            {
                throw new Exception("An error has ocurred. Please contact your administrator");
            }

            T res = new();

            if (response.Content == null || response.StatusCode == HttpStatusCode.RequestHeaderFieldsTooLarge)
            {
                return res;
            }

            var jsonStream = await response.Content.ReadAsStreamAsync();
            var deserializedDictionary = await JsonSerializer.DeserializeAsync<Dictionary<string, T>>(jsonStream, _jsonSerializerOptions);
            return deserializedDictionary.First().Value; // Only ever one item within the response top level JSON node
        }

        private string ExtractXDescription(HttpResponseMessage response)
        {
            if (response.Headers.TryGetValues("X-Description", out var values))
            {
                return values.FirstOrDefault();
            }

            return null;
        }
    }
}