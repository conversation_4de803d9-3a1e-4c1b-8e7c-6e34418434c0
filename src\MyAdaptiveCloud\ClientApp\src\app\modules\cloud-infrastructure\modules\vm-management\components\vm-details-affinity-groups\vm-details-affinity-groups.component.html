<div class="content-sub-heading d-flex justify-content-between">
    <div class="search-bar">
        <app-auto-search-box (filterCriteriaChanged)="onFilterCriteriaChanged($event)" />
    </div>
    @if (vmManagementPermissionService.canUpdateVmAffinityGroups() && (vmDetailsStateService.isLoading$ | async) ===
    false) {
    <div class="action-buttons">
        <div class="d-inline">
            <button class="btn btn-primary" data-testid="add-affinity-group-button" (click)="addToAffinityGroup()">Add
                to Affinity Group</button>
        </div>
    </div>
    }
</div>

<div class="card card-default">
    <div class="card-body">
        <ngx-datatable #table class='table bootstrap no-detail-row'>
        </ngx-datatable>
    </div>
</div>

<ng-template #headerTemplate let-column="column" let-sort="sortFn" let-sortDir="sortDir">
    <span (click)="sort()" class="clickable">
        {{ column.name }}
        <span
            [class]="sortDir === 'asc' ? 'datatable-icon-up sort-asc' : sortDir === 'desc' ? 'datatable-icon-down sort-desc' : 'datatable-icon-sort-unset'">
        </span>
    </span>
</ng-template>

<ng-template #actionsTemplate let-row="row">
    @if (toItem(row); as row) {
    <div class="text-center">
        <app-table-action [icon]="'far fa-trash-can'" [enabled]="(vmDetailsStateService.isLoading$ | async) === false"
            (clickHandler)="removeAffinityGroup(row.id)" [title]="'Remove Virtual Machine from Affinity Group'" />
    </div>
    }
</ng-template>
