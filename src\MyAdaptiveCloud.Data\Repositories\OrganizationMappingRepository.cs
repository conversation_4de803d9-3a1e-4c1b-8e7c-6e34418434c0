using Microsoft.EntityFrameworkCore;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.Contexts;
using MyAdaptiveCloud.Data.MyAdaptiveCloud.OrganizationMappings;

namespace MyAdaptiveCloud.Data.Repositories
{
    public class OrganizationMappingRepository : IOrganizationMappingRepository
    {
        private readonly MyAdaptiveCloudContext _dbContext;

        public OrganizationMappingRepository(MyAdaptiveCloudContext dbContext)
        {
            _dbContext = dbContext;
        }

        public Task<CloudInfraOrganizationMapping> GetCloudInfraMapping(Guid id)
        {
            return _dbContext.CloudInfraOrganizationMapping
                .AsNoTracking()
                .IgnoreQueryFilters() // Ignore Organization.IsActive filter. Remove this line if it is determined that Organization.IsActive is needed.
                .FirstOrDefaultAsync(mapping => (mapping.DomainId == id || mapping.AccountId == id));
        }

        public Task<BillingDBOrganizationMapping> GetBillingDBMapping(int id)
        {
            return _dbContext.BillingDBOrganizationMapping
                .AsNoTracking()
                .IgnoreQueryFilters() // Ignore Organization.IsActive filter. Remove this line if it is determined that Organization.IsActive is needed.
                .FirstOrDefaultAsync(mapping => mapping.CompanyId == id);
        }

        public Task<ConnectWiseOrganizationMapping> GetConnectWiseMapping(int id)
        {
            return _dbContext.ConnectWiseOrganizationMapping
                .IgnoreQueryFilters() // Ignore Organization.IsActive filter. Remove this line if it is determined that Organization.IsActive is needed.
                .AsNoTracking()
                .FirstOrDefaultAsync(mapping => mapping.CompanyId == id);
        }

        public Task<DataProtectionOrganizationMapping> GetDataProtectionMapping(string id)
        {
            return _dbContext.DataProtectionOrganizationMapping
                .AsNoTracking()
                .IgnoreQueryFilters() // Ignore Organization.IsActive filter. Remove this line if it is determined that Organization.IsActive is needed.
                .FirstOrDefaultAsync(mapping => mapping.SecondaryId == id || mapping.PrimaryId == id);
        }

        public async Task<List<Guid>> GetCloudInfraExistingMappingIds(int organizationId, IEnumerable<Guid> accountIds)
        {
            var stringAccountIds = accountIds.Select(id => id.ToString());
            var existingCloudInfraOrganizaionMappings = await _dbContext.CloudInfraOrganizationMapping.AsNoTracking()
                .IgnoreQueryFilters() // Ignore Organization.IsActive filter. Remove this line if it is determined that Organization.IsActive is needed.
                .Where(mapping => (accountIds.Contains(mapping.AccountId) || (mapping.DomainId.HasValue && accountIds.Contains(mapping.DomainId.Value))) &&
                                  mapping.OrganizationId != organizationId)
                .Select(mapping => new { mapping.AccountId, mapping.DomainId })
                .ToListAsync();

            return existingCloudInfraOrganizaionMappings.SelectMany(organizationMapping =>
            {
                var res = new List<Guid> { organizationMapping.AccountId };
                if (organizationMapping.DomainId.HasValue)
                    res.Add(organizationMapping.DomainId.Value);
                return res;
            }).ToList();
        }

        public async Task<List<string>> GetMappedCSNByCompany(int companyId)
        {
            return await _dbContext.BillingDBOrganizationMapping.AsNoTracking()
                .IgnoreQueryFilters() // Ignore Organization.IsActive filter. Remove this line if it is determined that Organization.IsActive is needed.
                .Include(mapping => mapping.Organization)
                .Where(mapping => mapping.CompanyId == companyId)
                .Select(mapping => mapping.Organization.Name).ToListAsync();
        }

        public IQueryable<BillingDBOrganizationMapping> GetBillingDbMappings()
        {
            return _dbContext.BillingDBOrganizationMapping.AsNoTracking()
                .IgnoreQueryFilters(); // Ignore Organization.IsActive filter. TODO: Remove this line if it is determined that Organization.IsActive is needed.
        }

        public async Task<CloudInfraOrganizationMapping> GetCloudInfraMappingByOrganization(int organizationId, bool asNoTracking = false)
        {
            var query = _dbContext.CloudInfraOrganizationMapping
                .IgnoreQueryFilters() // Ignore Organization.IsActive filter. Remove this line if it is determined that Organization.IsActive is needed.
                .Where(a => a.OrganizationId == organizationId);
            return await (asNoTracking ? query.AsNoTracking() : query).FirstOrDefaultAsync();
        }

        public async Task<BillingDBOrganizationMapping> GetBillingDBMappingByOrganization(int organizationId, bool asNoTracking = false)
        {
            var query = _dbContext.BillingDBOrganizationMapping.Where(a => a.OrganizationId == organizationId)
                .IgnoreQueryFilters(); // Ignore Organization.IsActive filter. TODO: Remove this line if it is determined that Organization.IsActive is needed.
            return await (asNoTracking ? query.AsNoTracking() : query).FirstOrDefaultAsync();
        }

        public async Task<ConnectWiseOrganizationMapping> GetConnectWiseMappingByOrganization(int organizationId, bool asNoTracking = false)
        {
            var query = _dbContext.ConnectWiseOrganizationMapping.Where(a => a.OrganizationId == organizationId)
                .IgnoreQueryFilters(); // Ignore Organization.IsActive filter. Remove this line if it is determined that Organization.IsActive is needed.

            return await (asNoTracking ? query.AsNoTracking() : query).FirstOrDefaultAsync();
        }

        public async Task<DataProtectionOrganizationMapping> GetDataProtectionMappingByOrganization(int organizationId, bool asNoTracking = false)
        {
            var query = _dbContext.DataProtectionOrganizationMapping
                .IgnoreQueryFilters() // Ignore Organization.IsActive filter. Remove this line if it is determined that Organization.IsActive is needed.
                .Where(a => a.OrganizationId == organizationId);
            return await (asNoTracking ? query.AsNoTracking() : query).FirstOrDefaultAsync();
        }

        public async Task<TenaxOrganizationMapping> GetTenaxMappingByOrganization(int organizationId, bool asNoTracking = false)
        {
            var query = _dbContext.TenaxOrganizationMapping
                .IgnoreQueryFilters() // Ignore Organization.IsActive filter. Remove this line if it is determined that Organization.IsActive is needed.
                .Where(a => a.OrganizationId == organizationId);
            return await (asNoTracking ? query.AsNoTracking() : query).FirstOrDefaultAsync();
        }

        public async Task<ConnectWiseOrganizationMapping> GetConnectWiseMappingWithOrg(int id, bool asNoTracking = false)
        {
            var query = _dbContext.ConnectWiseOrganizationMapping
                .Include(i => i.Organization)
                .Where(mapping => mapping.CompanyId == id);

            return await (asNoTracking ? query.AsNoTracking() : query).FirstOrDefaultAsync();
        }

        public async Task<CloudInfraOrganizationMapping> GetCloudInfraMappingWithOrg(Guid id, bool asNoTracking = false)
        {
            var query = _dbContext.CloudInfraOrganizationMapping
                .Include(i => i.Organization)
                .Where(mapping => mapping.DomainId == id || mapping.AccountId == id);

            return await (asNoTracking ? query.AsNoTracking() : query).FirstOrDefaultAsync();
        }

        public async Task<List<Tuple<int, string>>> GetCompanyShortNameByOrganizations(List<int> organizationIds)
        {
            return await _dbContext.BillingDBOrganizationMapping.AsNoTracking()
                .IgnoreQueryFilters() // Ignore Organization.IsActive filter. TODO: Remove this line if it is determined that Organization.IsActive is needed.
                .Where(om => organizationIds.Contains(om.OrganizationId))
                .Select(om => new Tuple<int, string>(om.OrganizationId, om.CompanyShortName)).ToListAsync();
        }

        public async Task DeleteDataProtectionMapping(string primaryId)
        {
            await _dbContext.DataProtectionOrganizationMapping
                .IgnoreQueryFilters() // Ignore Organization.IsActive filter. Remove this line if it is determined that Organization.IsActive is needed.
                .Where(om => om.PrimaryId == primaryId).ExecuteDeleteAsync();
        }

        public IQueryable<ConnectWiseOrganizationMapping> GetConnectWiseOrganizationMappings()
        {
            return _dbContext.ConnectWiseOrganizationMapping.AsNoTracking();
        }
    }
}