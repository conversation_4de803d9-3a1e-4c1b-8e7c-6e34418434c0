import { inject, Injectable } from '@angular/core';
import { CloudInfraPermissionService } from '@app/modules/cloud-infrastructure/services/cloud-infra-permission.service';
import { NETWORKING_ENDPOINT_NAMES } from '../models/networking.constants';

@Injectable({
    providedIn: 'root'
})
export class NetworkingPermissionService {

    private readonly cloudInfraPermissionService = inject(CloudInfraPermissionService);

    public canViewNetworkList(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.listNetworks);
    }

    public canViewVpnUserList(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.listVpnUsers);
    }

    public canViewVirtualPrivateCloudList(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.listVirtualPrivateClouds);
    }

    public canViewRemoteVpnGatewaysList(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.listRemoteVpnGateways);
    }

    public canRestartNetwork(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.restartNetwork);
    }

    public canCreateNetwork(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.createNetwork);
    }

    public canDeleteNetwork(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.deleteNetwork);
    }

    public canEditNetwork(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.editNetwork);
    }

    public canAddVpnUser(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.addVpnUser);
    }

    public canDeleteVpnUser(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.deleteVpnUser);
    }

    public canRestartVirtualPrivateCloud(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.restartNetwork);
    }

    public canDeleteVirtualPrivateCloud(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.deleteVirtualPrivateCloud);
    }

    public canCreateVirtualPrivateCloud(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || (this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.createNetwork) && this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.listVirtualPrivateCloudOfferings));
    }

    public canEditVirtualPrivateCloud(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.editVirtualPrivateCloud);
    }

    public canCreateRemoteVpnGateway(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.createRemoteVpnGateway);
    }

    public canEditRemoteVpnGateway(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.editRemoteVpnGateway);
    }

    public canDeleteRemoteVpnGateway(): boolean {
        return this.cloudInfraPermissionService.hasAllPermissions() || this.cloudInfraPermissionService.hasPermission(NETWORKING_ENDPOINT_NAMES.deleteRemoteVpnGateway);
    }

}
