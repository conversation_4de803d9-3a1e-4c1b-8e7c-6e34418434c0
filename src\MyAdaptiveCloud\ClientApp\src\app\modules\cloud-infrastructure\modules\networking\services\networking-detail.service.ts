import { Injectable, signal } from '@angular/core';
import { Network } from '@app/modules/cloud-infrastructure/models/network';

@Injectable({
    providedIn: 'root'
})
export class NetworkingDetailService {

    readonly selectedNetwork = signal<Network | null>(null);

    readonly isEditMode = signal<boolean>(false);

    updateSelectedNetwork(name: string, description: string) {
        this.selectedNetwork.update(network => ({
            ...network,
            name,
            displaytext: description
        }));
    }
}
