import os
from typing import Final

import pytest

from tests.api.schemas.tenax import expected_response_schemas
from tests.api.utils.handy_functions import (
    custom_sort_key_by_all_types,
)
from tests.api.utils.request import APIRequest
from tests.api.utils.schema_validator import validate_response_schema

ORG_ID: Final[int] = int(os.environ["ORG_ID"])
TENAX_TEST_SECRET_KEY: Final[str] = os.environ["TENAX_TEST_SECRET_KEY"]


pytest.skip(
    reason="TENAX will be skipped until: "
    "- Provisioning to be enabled in PROD "
    "- We have a stable instance of TENAX",
    allow_module_level=True,
)


@pytest.fixture(scope="session", autouse=True)
def set_tenax_feature_flag_and_provisioning(root_session: APIRequest) -> None:
    """
    Set up Tenax Feature Flag.

    Configure Tenax Feature Flag's Secret Key to use Tenax Test and do the Provisioning, if it is not provisioned.
    """
    # Set up the Tenax FF Secret Key
    route_str = "/api/configuration"
    payload = {"values": [{"id": 145, "value": TENAX_TEST_SECRET_KEY}]}
    response = root_session.post(route=route_str, payload=payload)
    assert response.status_code == 201
    assert response.text_dict["message"] == "Configuration updated successfully."

    # If it is not Provisioned, do the Provisioning
    route_str = "/api/tenax/0/isProvisioned"
    response = root_session.get(route=route_str)
    if not response.text_dict["data"]:
        route_str = "/api/tenax/0"
        root_session.post(route=route_str)
        assert response.status_code == 200


@pytest.mark.root_test
def test_get_tenax_provisioned_root(root_session: APIRequest) -> None:
    # Tenax is not provided by default with Root, but it is often provided to perform manual testing
    route_str = "/api/tenax/0/isProvisioned"
    response = root_session.get(route=route_str)
    assert response.status_code == 200
    assert response.headers["Content-Type"] == "application/json; charset=utf-8"
    assert response.elapsed_seconds < 5
    assert isinstance(response.text_dict["data"], bool)
    assert response.text_dict["message"] is None


def test_get_tenax_provisioned_admin(session: APIRequest) -> None:
    # Behavior changed in MYAC-1973, now org admin returns 200, confirmed by Eduardo
    route_str = f"/api/tenax/{ORG_ID}/isProvisioned"
    response = session.get(route=route_str)
    assert response.status_code == 200
    assert response.headers["Content-Type"] == "application/json; charset=utf-8"
    assert response.elapsed_seconds < 5
    assert response.text_dict["data"] is False
    assert response.text_dict["message"] is None


@pytest.mark.root_test
def test_get_tenax_by_organization(root_session: APIRequest) -> None:
    route_str = "/api/tenax/0"
    response = root_session.get(route=route_str)
    assert response.status_code == 200
    assert response.elapsed_seconds < 5
    validate_response_schema(response.text_dict, expected_response_schemas["tenax_by_org"])


@pytest.mark.root_test
def test_get_tenax_users(root_session: APIRequest) -> None:
    route_str = "/api/tenax/0/users"
    response = root_session.get(route=route_str)
    assert response.status_code == 200
    assert response.elapsed_seconds < 5
    validate_response_schema(response.text_dict, expected_response_schemas["tenax_org_users"])


@pytest.mark.root_test
@pytest.mark.parametrize(
    "query_params",
    [
        {"pageSize": 10, "currentPage": "1", "orderBy": "lastName", "orderDir": "asc"},
        {"pageSize": 10, "currentPage": "1", "orderBy": "lastName", "orderDir": "desc"},
        {"pageSize": 10, "currentPage": "1", "orderBy": "firstName", "orderDir": "asc"},
        {"pageSize": 10, "currentPage": "1", "orderBy": "firstName", "orderDir": "desc"},
        {"pageSize": 10, "currentPage": "1", "orderBy": "email", "orderDir": "asc"},
        {"pageSize": 10, "currentPage": "1", "orderBy": "email", "orderDir": "desc"},
        {"pageSize": 10, "currentPage": "1", "orderBy": "status", "orderDir": "asc"},
        {"pageSize": 10, "currentPage": "1", "orderBy": "status", "orderDir": "desc"},
        {"pageSize": 10, "currentPage": "1", "orderBy": "lastLogin", "orderDir": "asc"},
        {"pageSize": 10, "currentPage": "1", "orderBy": "lastLogin", "orderDir": "desc"},
    ],
    ids=[
        "Order By: Last Name. Dir: ASC",
        "Order By: Last Name. Dir: DESC",
        "Order By: First Name. Dir: ASC",
        "Order By: First Name. Dir: DESC",
        "Order By: Email. Dir: ASC",
        "Order By: Email. Dir: DESC",
        "Order By: Status. Dir: ASC",
        "Order By: Status. Dir: DESC",
        "Order By: Last Login. Dir: ASC",
        "Order By: Last Login. Dir: DESC",
    ],
)
def test_tenax_users_order_by(root_session: APIRequest, query_params: dict) -> None:
    is_reversed = bool(query_params["orderDir"] == "desc")
    order_dir = query_params["orderDir"]
    order_by = query_params["orderBy"]

    route_str = "/api/tenax/0/users"
    response = root_session.get(route=route_str, params=query_params)
    assert response.status_code == 200

    assert len(response.text_dict["data"]) == query_params["pageSize"]
    # Assert all OrderBy column in page are in order
    tenax_ordered_by = [tenax[order_by] for tenax in response.text_dict["data"]]
    tenax_sorted = sorted(tenax_ordered_by, key=custom_sort_key_by_all_types, reverse=is_reversed)
    # Remove items that contain "+" (used for email aliases) that break the email ordering
    if order_by == "email":
        tenax_ordered_by = [item for item in tenax_ordered_by if "+" not in item]
        tenax_sorted = [item for item in tenax_sorted if "+" not in item]
    assert tenax_ordered_by == tenax_sorted, f"Order by {order_by} column are not sorted in {order_dir} order."


@pytest.mark.root_test
def test_search_tenax_users(root_session: APIRequest) -> None:
    route_str = "/api/tenax/0/users"
    # Search by Greg's email
    query_param = {"searchTerm": "<EMAIL>"}
    response = root_session.get(route=route_str, params=query_param)
    assert response.status_code == 200
    assert response.elapsed_seconds < 5
    assert len(response.text_dict["data"]) == 1


@pytest.mark.root_test
def test_get_tenax_users_available(root_session: APIRequest) -> None:
    route_str = "/api/tenax/0/users/available"
    response = root_session.get(route=route_str)
    assert response.status_code == 200
    assert response.elapsed_seconds < 5
    validate_response_schema(response.text_dict, expected_response_schemas["tenax_users_available"])


@pytest.mark.root_test
def test_get_tenax_roles(root_session: APIRequest) -> None:
    route_str = "/api/tenax/0/roles"
    response = root_session.get(route=route_str)
    assert response.status_code == 200
    assert response.elapsed_seconds < 5
    assert len(response.text_dict["data"]) == 1
    assert response.text_dict["data"][0] == "ROLE_MASTER"


@pytest.mark.negative_test
@pytest.mark.root_test
def test_provision_already_provisioned_tenax(root_session: APIRequest) -> None:
    route_str = "/api/tenax/0"
    response = root_session.post(route=route_str, raise_errors=False)
    assert response.status_code == 400
    assert response.elapsed_seconds < 5
    assert response.text_dict["message"] == "Organization already provisioned."
